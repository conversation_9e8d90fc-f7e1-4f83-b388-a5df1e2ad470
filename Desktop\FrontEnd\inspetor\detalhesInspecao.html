<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes da Inspeção - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />    <style>
        [x-cloak] {
            display: none !important;
        }
        .severity-critical { background-color: #fee2e2; color: #991b1b; }
        .severity-high { background-color: #fed7aa; color: #9a3412; }
        .severity-medium { background-color: #fef3c7; color: #92400e; }
        .severity-low { background-color: #dcfce7; color: #166534; }

        .toggle-icon {
            display: flex;
            width: 24px;
            height: 24px;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .toggle-icon:hover {
            background-color: #e5e7eb;
            /* gray-200 */
        }

        .toggle-icon.open {
            transform: rotate(90deg);
        }

        .hierarchical-item-children {
            padding: 0.75rem 1rem;
            background-color: #fdfdff;
            border-top: 1px solid #e5e7eb;
        }

        .hierarchical-item-children .hierarchical-item {
            margin-left: 1rem;
        }


        .patologia-item {
            background-color: #f3f4f6;
            padding: 0.75rem;
            border-radius: 0.375rem;
            margin-top: 0.5rem;
            color: #374151;
            border: 1px solid #e0e0e0;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
        }

        .patologia-item strong {
            color: #1f2937;
            display: block;
            margin-bottom: 0.25rem;
        }

        .patologia-item .patologia-details {
            font-size: 0.875rem;
            /* text-sm */
            color: #4b5563;
            /* gray-600 */
            margin-bottom: 0.5rem;
        }

        .patologia-item .patologia-observacoes {
            /* For patologia description/observations */
            font-size: 0.875rem;
            color: #374151;
            /* Slightly darker for main text */
            margin-top: 0.35rem;
            padding: 0.35rem;
            background-color: #e9e9e9;
            border-radius: 0.25rem;
        }

        .action-button {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            border: 1px solid transparent;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .action-button:hover {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .btn-request-team-change {
            background-color: #4f46e5;
            color: white;
            border-color: #4338ca;
        }

        .btn-request-team-change:hover {
            background-color: #4338ca;
        }

        .btn-edit-details {
            background-color: #d97706;
            color: white;
            border-color: #b45309;
        }

        .btn-edit-details:hover {
            background-color: #b45309;
        }

        .modal-bg {
            background-color: #ffffff;
            border: 1px solid #e5e7eb;
        }

        .modal-input,
        .modal-select,
        .modal-textarea {
            background-color: #f9fafb;
            border-color: #d1d5db;
            color: #111827;
            border-radius: 0.375rem;
            /* rounded-md */
        }

        .modal-input:disabled,
        .modal-select:disabled {
            background-color: #e5e7eb;
            color: #6b7280;
            cursor: not-allowed;
        }

        .modal-input::placeholder {
            color: #9ca3af;
        }

        .form-error {
            color: #ef4444;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
    </style>
</head>

<body x-data="{ notificationsOpen: false, user: { avatar: '../ícones/perfil.png'} }" class="text-gray-700">
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex items-center gap-2">
                    <button
                        class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900">
                        <img src="../ícones/perfil.png" alt="Perfil" width="32px" height="32px">
                        <span>João Silva</span>
                    </button>
                </div>
            </header>

            <div class="px-40 py-5 flex-1" x-data="inspectionDetails()">
                <div class="flex flex-col gap-6">
                    <!-- Header com botão voltar -->
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" 
                            class="flex items-center gap-2 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="m12 19-7-7 7-7" />
                                <path d="M19 12H5" />
                            </svg>
                            <span class="ml-2">Voltar</span>
                        </button>
                    </div>

                    <h1 class="text-[#111518] text-2xl font-bold leading-tight" x-text="'Inspeção: ' + inspection.name"></h1>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">ID:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.id"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Nome:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Tipo:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Data de Criação:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.createdDate"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Status e Progresso</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Status:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(inspection.status)" x-text="inspection.status"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Progresso:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.progress + '%'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Inspetor:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.inspector"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Prazo:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="inspection.deadline || 'Não definido'"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5] col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrição</h3>
                            <textarea x-model="inspection.description" 
                                class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                rows="3"
                                placeholder="Adicione uma descrição da inspeção..."></textarea>
                            <button @click="saveDescription()" 
                                class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                Salvar Descrição
                            </button>                        </div>

                        <!-- Seção de Fotos da Inspeção -->
                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">Fotos da Inspeção</h3>
                                <button onclick="openAddPhotoModal('inspecao', inspection.id)" 
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                        <polyline points="7,10 12,15 17,10"/>
                                        <line x1="12" y1="15" x2="12" y2="3"/>
                                    </svg>
                                    Adicionar Foto
                                </button>
                            </div>
                            <div id="fotosInspecaoContainer" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <!-- Fotos serão carregadas aqui dinamicamente -->
                                <div class="text-center py-8 text-gray-500 col-span-full" id="noPhotosMessage">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                        <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                        <circle cx="9" cy="9" r="2"/>
                                        <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                                    </svg>
                                    <p>Nenhuma foto adicionada ainda</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de Edifícios -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Edifícios Inspecionados</h3>
                        <div class="space-y-3">
                            <template x-for="building in inspection.buildings" :key="building.id">
                                <div class="flex items-center gap-4 p-4 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50"
                                    @click="viewBuildingDetails(building.id)">
                                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg w-12 h-12"
                                        :style="`background-image: url('${building.photo || '../ícones/disruption.png'}')`">
                                    </div>
                                    <div class="flex flex-col justify-center flex-1">
                                        <p class="text-[#111518] text-base font-medium leading-normal" x-text="building.name">
                                        </p>
                                        <p class="text-[#637588] text-sm font-normal leading-normal"
                                            x-text="building.address"></p>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :class="getStatusColor(building.status)" x-text="building.status"></span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round">
                                            <path d="m9 18 6-6-6-6" />
                                        </svg>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>                </div>
            </div>
        </div>

    <div id="genericModal"
        class="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full flex items-center justify-center"
        style="display: none; z-index: 50;">
        <div class="relative modal-bg mx-auto p-6 border w-full max-w-2xl shadow-xl rounded-md">
            <div class="flex justify-between items-center mb-4">
                <h3 id="modalTitle" class="text-xl leading-6 font-semibold text-gray-900">Título do Modal</h3>
                <button onclick="closeModal()"
                    class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div id="modalBody" class="text-gray-700 max-h-[70vh] overflow-y-auto pr-2 space-y-4">
            </div>
            <div id="modalFooter"
                class="flex justify-end items-center px-4 py-3 gap-3 text-right mt-6 border-t border-gray-200">
            </div>
        </div>
    </div>

    <!-- Modal de Gerenciamento de Fotos -->
    <div id="photoModal" class="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full flex items-center justify-center" style="display: none; z-index: 50;">
        <div class="relative modal-bg mx-auto p-6 border w-full max-w-lg shadow-xl rounded-md">
            <div class="flex justify-between items-center mb-4">
                <h3 id="photoModalTitle" class="text-xl leading-6 font-semibold text-gray-900">Gerenciar Foto</h3>
                <button onclick="closePhotoModal()" class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <form id="photoForm" class="space-y-4">
                <div>
                    <label for="photoUpload" class="block text-sm font-medium text-gray-700 mb-2">Selecionar Foto</label>
                    <input type="file" id="photoUpload" name="photo" accept="image/*" class="modal-input w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label for="photoDescription" class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                    <textarea id="photoDescription" name="description" rows="3" class="modal-textarea w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Descreva a foto..."></textarea>
                </div>
                <div class="flex justify-end gap-3 pt-4">
                    <button type="button" onclick="closePhotoModal()" class="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors">
                        Cancelar
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <span id="photoSubmitText">Adicionar</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>        function inspectionDetails() {
            return {
                inspection: {},
                
                init() {
                    this.loadInspection();
                },

                loadInspection() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const inspectionId = urlParams.get('id') || 'INSP-001';
                    
                    // Carrega dados da inspeção
                    if (inspectionsData[inspectionId]) {
                        this.inspection = {
                            ...inspectionsData[inspectionId],
                            buildings: this.mapEdificiosToBuildings(inspectionsData[inspectionId].edificios || [])
                        };
                    } else {
                        this.inspection = {
                            id: inspectionId,
                            name: 'Inspeção não encontrada',
                            status: 'Indefinido',
                            type: 'N/A',
                            createdDate: 'N/A',
                            progress: 0,
                            buildings: []
                        };
                    }
                },

                mapEdificiosToBuildings(edificios) {
                    return edificios.map(edificio => ({
                        id: edificio.id,
                        name: edificio.nome,
                        address: edificio.endereco,
                        status: 'Em Andamento', // Status padrão
                        photo: edificio.photo || '../ícones/disruption.png'
                    }));
                },

                getStatusColor(status) {
                    const statusColors = {
                        'Agendada': 'bg-yellow-100 text-yellow-800',
                        'Em Andamento': 'bg-blue-100 text-blue-800',
                        'Concluída': 'bg-green-100 text-green-800',
                        'Cancelada': 'bg-red-100 text-red-800',
                        'Pausada': 'bg-gray-100 text-gray-800'
                    };
                    return statusColors[status] || 'bg-gray-100 text-gray-800';
                },

                saveDescription() {
                    // Implementar lógica de salvamento
                    console.log('Descrição salva:', this.inspection.description);
                    alert('Descrição salva com sucesso!');
                },

                viewBuildingDetails(buildingId) {
                    // Navegar para detalhes do edifício
                    window.location.href = `detalhesEdificio.html?id=${buildingId}&inspectionId=${this.inspection.id}`;
                }
            }
        }

        let currentInspectionId = null;

        // Estrutura de dados unificada, similar à do admin
        const inspectionsData = {            "INSP-001": {
                id: "INSP-001",
                name: "Inspeção Edifício Central",
                address: "Rua Principal, 123, Centro",
                startDate: "15/03/2024",
                endDate: "",
                status: "Agendada",
                coordinator: "Carlos Silva",
                engineer: "Carlos Silva",
                client: "Edifício Central",
                type: "Estrutural",
                description: "Inspeção inicial da estrutura do edifício.",
                contactName: "Ana Paula",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 98765-4321",
                progress: 0,
                inspector: "João Silva",
                deadline: "30/03/2024",
                createdDate: "01/03/2024",
                inspectors: ["João Kleber", "Maria Fernanda"],
                edificios: [
                    {
                        id: "EDF-001",
                        nome: "Bloco A",
                        endereco: "Rua das Palmeiras, 123 - Bloco A",
                        descricao: "Edifício residencial de 10 andares.",
                        pavimentos: [
                            {
                                id: "PAV-001",
                                edificioId: "EDF-001",
                                nome: "Térreo",
                                numero: 0,
                                descricao: "Áreas comuns e garagens.",
                                ambientes: [
                                    {
                                        id: "AMB-001",
                                        pavimentoId: "PAV-001",
                                        nome: "Hall de Entrada",
                                        tipo: "Área Comum",
                                        descricao: "Entrada principal do Bloco A.",
                                        sistemas: [
                                            {
                                                id: "SIS-001",
                                                ambienteId: "AMB-001",
                                                nome: "Iluminação",
                                                tipo: "Elétrico",
                                                descricao: "Sistema de iluminação do hall.",
                                                patologias: [
                                                    { id: "PAT-001", sistemaId: "SIS-001", nome: "Lâmpada Queimada", descricao: "Lâmpada da entrada queimada.", tipo: "Elétrica", criticidade: "Baixa", observacoes: "Substituir lâmpada.", foto: null, status: "Não Resolvida" }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },            "INSP-002": {
                id: "INSP-002",
                name: "Inspeção Shopping Plaza",
                address: "Av. Comercial, 456, Centro Comercial",
                startDate: "10/03/2024",
                endDate: "",
                status: "Em Andamento",
                coordinator: "Maria Santos",
                engineer: "João Silva",
                client: "Shopping Plaza",
                type: "Elétrica",
                description: "Inspeção das instalações elétricas do shopping.",
                contactName: "Pedro Costa",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 91234-5678",
                progress: 45,
                inspectors: ["Carlos Alberto", "Juliana Paes"],
                edificios: [] // Adicionar edifícios conforme necessário
            },
            "INSP-003": {
                id: "INSP-003",
                name: "Inspeção Residencial Aurora",
                address: "Rua das Flores, 789, Bairro Residencial",
                startDate: "25/02/2024",                endDate: "12/03/2024",
                status: "Concluída",
                coordinator: "Ana Lima",
                engineer: "Carlos Silva",
                client: "Condomínio Aurora",
                type: "Hidráulica",
                description: "Inspeção completa do sistema hidráulico.",
                contactName: "Roberto Silva",
                contactEmail: "<EMAIL>",
                contactPhone: "(11) 95555-4444",
                progress: 100,
                inspectors: ["Lucas Mendes"],
                edificios: [] // Adicionar edifícios conforme necessário
            }
        };

        function createItemHtml(titleHtml, descriptionText) {
            let html = `<span class="hierarchical-item-title">${titleHtml}</span>`;
            if (descriptionText) { }
            return html;
        }        function renderHierarquia(edificios, inspectionIdParam) {
            const container = document.getElementById('hierarquia-container');
            container.innerHTML = '';
            const actualInspectionId = inspectionIdParam || currentInspectionId;

            if (!edificios || edificios.length === 0) {
                container.innerHTML = '<p class="text-sm text-gray-500 py-2">Nenhum edifício cadastrado para esta inspeção.</p>'; // Mensagem se não houver edifícios
                return;
            }

            edificios.forEach(edificio => {
                const titleHtml = `<span class="item-type-label">Edifício:</span> ${edificio.nome}`;
                const edIdBase = `edf-${actualInspectionId}-${edificio.id}`;
                const edDiv = createHierarchicalItem(
                    createItemHtml(titleHtml, edificio.descricao),
                    edIdBase,
                    () => {
                        // Função para carregar pavimentos quando o edifício for expandido
                        const childrenContainer = document.getElementById(`children-${edIdBase}`);
                        if (childrenContainer && !childrenContainer.dataset.loaded) {
                            renderPavimentos(edificio.pavimentos, `children-${edIdBase}`, actualInspectionId, edificio.id);
                            childrenContainer.dataset.loaded = 'true'; // Marcar como carregado
                        }
                    },
                    'edificio',
                    { inspecaoId: actualInspectionId, edificioId: edificio.id }
                );
                container.appendChild(edDiv);
            });
        }        function renderPavimentos(pavimentos, parentId, inspectionId, edificioId) {
            const parentElement = document.getElementById(parentId);
            parentElement.innerHTML = '';
            if (!pavimentos || pavimentos.length === 0) {
                parentElement.innerHTML = '<p class="text-sm text-gray-500 py-2 pl-4">Nenhum pavimento cadastrado.</p>';
                return;
            }
            pavimentos.forEach(pavimento => {
                const titleHtml = `<span class="item-type-label">Pavimento:</span> ${pavimento.nome} <span class="item-details">(Nº ${pavimento.numero !== undefined ? pavimento.numero : 'N/D'})</span>`;
                const pavIdBase = `pav-${inspectionId}-${edificioId}-${pavimento.id}`;
                const pavDiv = createHierarchicalItem(
                    createItemHtml(titleHtml, pavimento.descricao),
                    pavIdBase,
                    () => {
                        const childrenContainer = document.getElementById(`children-${pavIdBase}`);
                        if (childrenContainer && !childrenContainer.dataset.loaded) {
                            renderAmbientes(pavimento.ambientes, `children-${pavIdBase}`, inspectionId, edificioId, pavimento.id);
                            childrenContainer.dataset.loaded = 'true';
                        }
                    },
                    'pavimento',
                    { inspecaoId: inspectionId, edificioId: edificioId, pavimentoId: pavimento.id }
                );
                parentElement.appendChild(pavDiv);
            });
        }        function renderAmbientes(ambientes, parentId, inspectionId, edificioId, pavimentoId) {
            const parentElement = document.getElementById(parentId);
            parentElement.innerHTML = '';
            if (!ambientes || ambientes.length === 0) {
                parentElement.innerHTML = '<p class="text-sm text-gray-500 py-2 pl-8">Nenhum ambiente cadastrado.</p>';
                return;
            }
            ambientes.forEach(ambiente => {
                const titleHtml = `<span class="item-type-label">Ambiente:</span> ${ambiente.nome} <span class="item-details">(${ambiente.tipo || 'N/D'})</span>`;
                const ambIdBase = `amb-${inspectionId}-${edificioId}-${pavimentoId}-${ambiente.id}`;
                const ambDiv = createHierarchicalItem(
                    createItemHtml(titleHtml, ambiente.descricao),
                    ambIdBase,
                    () => {
                        const childrenContainer = document.getElementById(`children-${ambIdBase}`);
                        if (childrenContainer && !childrenContainer.dataset.loaded) {
                            renderSistemas(ambiente.sistemas, `children-${ambIdBase}`, inspectionId, edificioId, pavimentoId, ambiente.id);
                            childrenContainer.dataset.loaded = 'true';
                        }
                    },
                    'ambiente',
                    { inspecaoId: inspectionId, edificioId: edificioId, pavimentoId: pavimentoId, ambienteId: ambiente.id }
                );
                parentElement.appendChild(ambDiv);
            });
        }        function renderSistemas(sistemas, parentId, inspectionId, edificioId, pavimentoId, ambienteId) {
            const parentElement = document.getElementById(parentId);
            parentElement.innerHTML = '';
            if (!sistemas || sistemas.length === 0) {
                parentElement.innerHTML = '<p class="text-sm text-gray-500 py-2 pl-12">Nenhum sistema cadastrado.</p>';
                return;
            }
            sistemas.forEach(sistema => {
                const titleHtml = `<span class="item-type-label">Sistema:</span> ${sistema.nome} <span class="item-details">(${sistema.tipo || 'N/D'})</span>`;
                const sisIdBase = `sis-${inspectionId}-${edificioId}-${pavimentoId}-${ambienteId}-${sistema.id}`;
                const sisDiv = createHierarchicalItem(
                    createItemHtml(titleHtml, sistema.descricao),
                    sisIdBase,
                    () => {
                        const childrenContainer = document.getElementById(`children-${sisIdBase}`);
                        if (childrenContainer && !childrenContainer.dataset.loaded) {
                            renderPatologias(sistema.patologias, `children-${sisIdBase}`, inspectionId, edificioId, pavimentoId, ambienteId, sistema.id);
                            childrenContainer.dataset.loaded = 'true';
                        }
                    },
                    'sistema',
                    { inspecaoId: inspectionId, edificioId: edificioId, pavimentoId: pavimentoId, ambienteId: ambienteId, sistemaId: sistema.id }
                );
                parentElement.appendChild(sisDiv);
            });
        }        function renderPatologias(patologias, parentId, inspectionId, edificioId, pavimentoId, ambienteId, sistemaId) {
            const parentElement = document.getElementById(parentId);
            parentElement.innerHTML = '';
            if (!patologias || patologias.length === 0) {
                parentElement.innerHTML = '<p class="text-sm text-gray-500 py-2 pl-16">Nenhuma patologia cadastrada.</p>';
                return;
            }
            patologias.forEach(patologia => {
                const patologiaDiv = document.createElement('div');
                patologiaDiv.className = 'patologia-item relative';
                let observacoesHtml = '';
                if (patologia.observacoes) {
                    observacoesHtml = `<div class="patologia-observacoes"><strong>Observações:</strong> ${patologia.observacoes}</div>`;
                }

                patologiaDiv.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <strong>${patologia.nome}</strong>
                            <div class="patologia-details">
                                <p><strong>Tipo:</strong> ${patologia.tipo || 'N/D'}</p>
                                <p><strong>Criticidade:</strong> ${patologia.criticidade || 'N/D'}</p>
                                <p><strong>Status:</strong> ${patologia.status || 'N/D'}</p>
                                ${patologia.descricao ? `<p><strong>Descrição:</strong> ${patologia.descricao}</p>` : ''}
                            </div>
                            ${observacoesHtml}
                        </div>
                        <button onclick="navigateToPatologia('${inspectionId}', '${edificioId}', '${pavimentoId}', '${ambienteId}', '${sistemaId}', '${patologia.id}')" class="action-button bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 ml-2 flex-shrink-0">
                            Ver Detalhes
                        </button>
                    </div>
                `;
                parentElement.appendChild(patologiaDiv);
            });
        }        function createHierarchicalItem(contentHtml, idBase, onExpandLoadChildren, itemType, params) {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'hierarchical-item';
            const childrenId = `children-${idBase}`;

            // Criar botão "Ver Detalhes" baseado no tipo do item
            let detailsButton = '';
            if (itemType && params) {
                const buttonOnClick = getNavigationFunction(itemType, params);
                detailsButton = `
                    <button onclick="${buttonOnClick}" class="action-button bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 mr-2">
                        Ver Detalhes
                    </button>
                `;
            }

            // Adiciona o ícone de toggle
            itemDiv.innerHTML = `
                <div class="hierarchical-item-header">
                    <div>${contentHtml}</div>
                    <div class="flex items-center">
                        ${detailsButton}
                        <span class="toggle-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-chevron-right" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z"/>
                            </svg>
                        </span>
                    </div>
                </div>
                <div id="${childrenId}" class="hierarchical-item-children" style="display: none;">
                    <!-- Conteúdo dos filhos será carregado aqui -->
                </div>
            `;

            const header = itemDiv.querySelector('.hierarchical-item-header');
            const toggleIconElement = header.querySelector('.toggle-icon'); // Seleciona o span que contém o SVG

            // Adicionar event listener apenas ao ícone de toggle, não ao header inteiro
            toggleIconElement.addEventListener('click', function (event) {
                event.stopPropagation(); // Prevenir que cliques no toggle ativem outros handlers
                const childrenContainer = document.getElementById(childrenId);
                const isOpening = childrenContainer.style.display === 'none';

                if (isOpening && typeof onExpandLoadChildren === 'function' && !childrenContainer.dataset.loaded) {
                    onExpandLoadChildren();
                }
                toggleChildren(childrenId, toggleIconElement);
            });

            return itemDiv;
        }        function toggleChildren(childrenId, iconElement) {
            const childrenContainer = document.getElementById(childrenId);
            if (childrenContainer.style.display === 'none') {
                childrenContainer.style.display = 'block';
                if (iconElement) iconElement.classList.add('open');
            } else {
                childrenContainer.style.display = 'none';
                if (iconElement) iconElement.classList.remove('open');
            }
        }

        // Funções de navegação para cada tipo de detalhe
        function getNavigationFunction(itemType, params) {
            switch (itemType) {
                case 'edificio':
                    return `navigateToEdificio('${params.inspecaoId}', '${params.edificioId}')`;
                case 'pavimento':
                    return `navigateToPavimento('${params.inspecaoId}', '${params.edificioId}', '${params.pavimentoId}')`;
                case 'ambiente':
                    return `navigateToAmbiente('${params.inspecaoId}', '${params.edificioId}', '${params.pavimentoId}', '${params.ambienteId}')`;
                case 'sistema':
                    return `navigateToSistema('${params.inspecaoId}', '${params.edificioId}', '${params.pavimentoId}', '${params.ambienteId}', '${params.sistemaId}')`;
                default:
                    return '';
            }
        }

        function navigateToEdificio(inspecaoId, edificioId) {
            window.location.href = `detalhesEdificio.html?inspecaoId=${inspecaoId}&edificioId=${edificioId}`;
        }

        function navigateToPavimento(inspecaoId, edificioId, pavimentoId) {
            window.location.href = `detalhesPavimento.html?inspecaoId=${inspecaoId}&edificioId=${edificioId}&pavimentoId=${pavimentoId}`;
        }

        function navigateToAmbiente(inspecaoId, edificioId, pavimentoId, ambienteId) {
            window.location.href = `detalhesAmbiente.html?inspecaoId=${inspecaoId}&edificioId=${edificioId}&pavimentoId=${pavimentoId}&ambienteId=${ambienteId}`;
        }

        function navigateToSistema(inspecaoId, edificioId, pavimentoId, ambienteId, sistemaId) {
            window.location.href = `detalhesSistema.html?inspecaoId=${inspecaoId}&edificioId=${edificioId}&pavimentoId=${pavimentoId}&ambienteId=${ambienteId}&sistemaId=${sistemaId}`;
        }

        function navigateToPatologia(inspecaoId, edificioId, pavimentoId, ambienteId, sistemaId, patologiaId) {
            window.location.href = `detalhesPatologia.html?inspecaoId=${inspecaoId}&edificioId=${edificioId}&pavimentoId=${pavimentoId}&ambienteId=${ambienteId}&sistemaId=${sistemaId}&patologiaId=${patologiaId}`;
        }

        function openModal(title, bodyContent, footerContent = '') {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = bodyContent;
            document.getElementById('modalFooter').innerHTML = footerContent;
            document.getElementById('genericModal').style.display = 'flex';
        }

        function closeModal() {
            document.getElementById('genericModal').style.display = 'none';
            document.getElementById('modalBody').innerHTML = '';
        }

        function getModalInputFields(fieldsConfig) {
            return fieldsConfig.map(field => `
                <div class="${field.colSpan || 'col-span-1'}">
                    <label for="${field.id}" class="block text-sm font-medium text-gray-700">${field.label}:</label>
                    ${field.type === 'textarea' ?
                    `<textarea id="${field.id}" name="${field.id}" rows="${field.rows || 3}" class="mt-1 block w-full modal-textarea shadow-sm sm:text-sm border-gray-300 rounded-md">${field.value || ''}</textarea>` :
                    field.type === 'select' ?
                        `<select id="${field.id}" name="${field.id}" class="mt-1 block w-full modal-input shadow-sm sm:text-sm border-gray-300 rounded-md">
                            ${(field.options || []).map(opt => `<option value="${opt.value}" ${opt.value === field.value ? 'selected' : ''}>${opt.text}</option>`).join('')}
                        </select>` :
                        `<input type="${field.type || 'text'}" id="${field.id}" name="${field.id}" value="${field.value || ''}" ${field.placeholder ? `placeholder="${field.placeholder}"` : ''} class="mt-1 block w-full modal-input shadow-sm sm:text-sm border-gray-300 rounded-md">`
                }
                    <p id="${field.id}Error" class="form-error"></p>
                </div>
            `).join('');
        }

        function validateModalForm(fieldsConfig) {
            let isValid = true;
            fieldsConfig.forEach(field => {
                const inputElement = document.getElementById(field.id);
                const errorElement = document.getElementById(`${field.id}Error`);
                if (field.required && !inputElement.value.trim()) {
                    errorElement.textContent = `${field.label} é obrigatório.`;
                    inputElement.classList.add('border-red-500');
                    isValid = false;
                } else {
                    errorElement.textContent = '';
                    inputElement.classList.remove('border-red-500');
                }
            });
            return isValid;
        }

        function openInspectionDetailsModal(inspId) {
            const inspection = inspectionsData[inspId];
            if (!inspection) { alert('Erro: Detalhes da inspeção não encontrados.'); return; }

            const modalTitle = `Editar Detalhes da Inspeção (#${inspId})`;
            // Campos alinhados com a estrutura de dados e o modal de admin (se aplicável)
            const fields = [
                { id: 'inspectName', label: 'Nome da Inspeção', value: inspection.name, required: true, colSpan: 'md:col-span-2' },
                { id: 'inspectAddress', label: 'Endereço', value: inspection.address, required: true, colSpan: 'md:col-span-2' },
                { id: 'inspectClient', label: 'Cliente', value: inspection.client, required: false, colSpan: 'md:col-span-2' }, // Adicionado Cliente
                { id: 'inspectType', label: 'Tipo de Inspeção', value: inspection.type, required: false }, // Adicionado Tipo
                { id: 'inspectStartDate', label: 'Data de Início (dd/mm/aaaa)', value: inspection.startDate, placeholder: 'dd/mm/aaaa', required: true },
                { id: 'inspectEndDate', label: 'Data de Término (dd/mm/aaaa)', value: inspection.endDate || '', placeholder: 'dd/mm/aaaa' },
                { id: 'inspectOverallDescription', label: 'Descrição da Inspeção', type: 'textarea', value: inspection.description, colSpan: 'md:col-span-2' },
                { id: 'inspectContactName', label: 'Nome do Contato', value: inspection.contactName, required: false },
                { id: 'inspectContactEmail', label: 'Email do Contato', value: inspection.contactEmail, type: 'email', required: false },
                { id: 'inspectContactPhone', label: 'Telefone do Contato', value: inspection.contactPhone, type: 'tel', required: false }
            ];

            const bodyContent = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                    ${getModalInputFields(fields)}
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Coordenador:</label>
                        <p class="mt-1 text-sm text-gray-900 p-2 bg-gray-100 rounded-md">${inspection.coordinator}</p>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Engenheiro Responsável:</label>
                        <p class="mt-1 text-sm text-gray-900 p-2 bg-gray-100 rounded-md">${inspection.engineer}</p>
                    </div>
                </div>`;

            const footerContent = `
                <button type="button" onclick="closeModal()" class="action-button bg-gray-200 hover:bg-gray-300 text-gray-800">Cancelar</button>
                <button type="button" onclick="saveInspectionDetails('${inspId}')" class="action-button btn-edit-details">Salvar Alterações</button>
            `;
            openModal(modalTitle, bodyContent, footerContent);
        }

        function saveInspectionDetails(inspId) {
            const inspection = inspectionsData[inspId];
            if (!inspection) { alert('Erro: Inspeção não encontrada para salvar.'); return; }

            const fieldsToValidate = [
                { id: 'inspectName', label: 'Nome da Inspeção', required: true },
                { id: 'inspectAddress', label: 'Endereço', required: true },
                { id: 'inspectClient', label: 'Cliente' },
                { id: 'inspectType', label: 'Tipo de Inspeção' },
                { id: 'inspectStartDate', label: 'Data de Início', required: true },
                { id: 'inspectEndDate', label: 'Data de Término' },
                { id: 'inspectOverallDescription', label: 'Descrição da Inspeção' },
                { id: 'inspectContactName', label: 'Nome do Contato' },
                { id: 'inspectContactEmail', label: 'Email do Contato' },
                { id: 'inspectContactPhone', label: 'Telefone do Contato' }
            ];

            const startDateInput = document.getElementById('inspectStartDate');
            const endDateInput = document.getElementById('inspectEndDate');
            const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;

            let baseValidation = validateModalForm(fieldsToValidate);
            let dateValidation = true;

            if (!dateRegex.test(startDateInput.value.trim())) {
                document.getElementById('inspectStartDateError').textContent = 'Formato de data inválido (dd/mm/aaaa).';
                startDateInput.classList.add('border-red-500');
                dateValidation = false;
            } else {
                document.getElementById('inspectStartDateError').textContent = '';
                startDateInput.classList.remove('border-red-500');
            }

            if (endDateInput.value.trim() && !dateRegex.test(endDateInput.value.trim())) {
                document.getElementById('inspectEndDateError').textContent = 'Formato de data inválido (dd/mm/aaaa).';
                endDateInput.classList.add('border-red-500');
                dateValidation = false;
            } else {
                document.getElementById('inspectEndDateError').textContent = '';
                endDateInput.classList.remove('border-red-500');
            }

            if (!baseValidation || !dateValidation) return;

            inspection.name = document.getElementById('inspectName').value.trim();
            inspection.address = document.getElementById('inspectAddress').value.trim();
            inspection.client = document.getElementById('inspectClient').value.trim(); // Salvar Cliente
            inspection.type = document.getElementById('inspectType').value.trim(); // Salvar Tipo
            inspection.startDate = startDateInput.value.trim();
            inspection.endDate = endDateInput.value.trim() || null;
            inspection.description = document.getElementById('inspectOverallDescription').value.trim();
            inspection.contactName = document.getElementById('inspectContactName').value.trim(); // Salvar Nome do Contato
            inspection.contactEmail = document.getElementById('inspectContactEmail').value.trim(); // Salvar Email do Contato
            inspection.contactPhone = document.getElementById('inspectContactPhone').value.trim(); // Salvar Telefone do Contato

            closeModal();
            renderPageDetails(inspId);
        }

        function openTeamChangeRequestModal(inspId) {
            const inspection = inspectionsData[inspId];
            if (!inspection) { alert('Erro: Inspeção não encontrada.'); return; }

            const modalTitle = `Solicitar Alteração na Equipe da Inspeção (#${inspId})`;
            const fields = [
                {
                    id: 'requestType', label: 'Tipo de Solicitação', type: 'select', value: 'add', options: [
                        { value: 'add', text: 'Adicionar Inspetor' }, { value: 'remove', text: 'Remover Inspetor' }
                    ]
                },
                { id: 'requestInspectorName', label: 'Nome do Inspetor', required: true },
                { id: 'requestJustification', label: 'Justificativa (Opcional)', type: 'textarea' }
            ];

            const bodyContent = `
                <div class="space-y-4">
                    ${getModalInputFields(fields)}
                    <div>
                        <p class="text-xs text-gray-500">Inspetores Atuais: ${inspection.inspectors.join(', ') || 'Nenhum'}</p>
                    </div>
                </div>`;
            const footerContent = `
                <button type="button" onclick="closeModal()" class="action-button bg-gray-200 hover:bg-gray-300 text-gray-800">Cancelar</button>
                <button type="button" onclick="submitTeamChangeRequest('${inspId}')" class="action-button btn-request-team-change">Enviar Solicitação</button>
            `;
            openModal(modalTitle, bodyContent, footerContent);
        }

        function submitTeamChangeRequest(inspId) {
            const fieldsToValidate = [{ id: 'requestInspectorName', label: 'Nome do Inspetor', required: true }];
            if (!validateModalForm(fieldsToValidate)) return;

            const requestType = document.getElementById('requestType').value;
            const inspectorName = document.getElementById('requestInspectorName').value.trim();
            const justification = document.getElementById('requestJustification').value.trim();

            console.log("Solicitação de Alteração de Equipe:", { inspId, requestType, inspectorName, justification });
            alert(`Solicitação para ${requestType === 'add' ? 'adicionar' : 'remover'} o inspetor '${inspectorName}' enviada para aprovação.`);
            closeModal();
        }

        function renderPageDetails(inspId) {
            currentInspectionId = inspId;
            const currentInspection = inspectionsData[inspId];
            const actionButtonsContainer = document.getElementById('action-buttons');
            const layoutContentContainer = document.querySelector('.layout-content-container');

            if (currentInspection) {
                document.getElementById('inspection-id-title').textContent = inspId ? `(#${inspId})` : '';
                document.getElementById('inspection-id').textContent = inspId;
                document.getElementById('inspection-name').textContent = currentInspection.name; // Usar 'name'
                document.getElementById('inspection-address').textContent = currentInspection.address;
                document.getElementById('inspection-coordinator').textContent = currentInspection.coordinator;
                // Adicionar os novos campos para exibição
                // Certifique-se de que os IDs HTML correspondentes existam na seção de detalhes da página
                const clientEl = document.getElementById('inspection-client');
                if (clientEl) clientEl.textContent = currentInspection.client || "Não informado";

                const typeEl = document.getElementById('inspection-type');
                if (typeEl) typeEl.textContent = currentInspection.type || "Não informado";

                const engineerEl = document.getElementById('inspection-engineer');
                if (engineerEl) engineerEl.textContent = currentInspection.engineer || "Não informado";
                
                const contactNameEl = document.getElementById('inspection-contact-name');
                if (contactNameEl) contactNameEl.textContent = currentInspection.contactName || "Não informado";

                const contactEmailEl = document.getElementById('inspection-contact-email');
                if (contactEmailEl) contactEmailEl.textContent = currentInspection.contactEmail || "Não informado";

                const contactPhoneEl = document.getElementById('inspection-contact-phone');
                if (contactPhoneEl) contactPhoneEl.textContent = currentInspection.contactPhone || "Não informado";                const statusEl = document.getElementById('inspection-status');
                statusEl.textContent = currentInspection.status;
                // Limpa classes de status e cores anteriores e define a base
                statusEl.className = 'font-medium px-2 py-1 rounded-md text-sm'; 

                if (currentInspection.status === "Agendada") {
                    statusEl.classList.add('bg-blue-100', 'text-blue-800');
                } else if (currentInspection.status === "Em Andamento") {
                    statusEl.classList.add('bg-yellow-100', 'text-yellow-800');
                } else if (currentInspection.status === "Concluída") {
                    statusEl.classList.add('bg-green-100', 'text-green-800');
                } else if (currentInspection.status === "Cancelada") {
                    statusEl.classList.add('bg-red-100', 'text-red-800'); 
                } else {
                    // Fallback para status desconhecido
                    statusEl.classList.add('bg-gray-100', 'text-gray-600');
                }

                document.getElementById('inspection-start-date').textContent = currentInspection.startDate;
                document.getElementById('inspection-end-date').textContent = currentInspection.endDate || "Não definida";
                document.getElementById('inspection-description').textContent = currentInspection.description || "Nenhuma descrição fornecida.";

                const inspetoresListEl = document.getElementById('inspetores-list');
                inspetoresListEl.innerHTML = '';
                if (currentInspection.inspectors && currentInspection.inspectors.length > 0) {
                    currentInspection.inspectors.forEach(insp => {
                        const p = document.createElement('p');
                        p.textContent = insp;
                        inspetoresListEl.appendChild(p);
                    });
                } else {
                    inspetoresListEl.textContent = "Nenhum inspetor designado.";
                }

                actionButtonsContainer.innerHTML = '';

                const btnEditDetails = document.createElement('button');
                btnEditDetails.className = 'action-button btn-edit-details';
                btnEditDetails.textContent = 'Editar Detalhes';
                btnEditDetails.onclick = () => openInspectionDetailsModal(inspId);
                actionButtonsContainer.appendChild(btnEditDetails);

                const btnRequestTeamChange = document.createElement('button');
                btnRequestTeamChange.className = 'action-button btn-request-team-change';
                btnRequestTeamChange.textContent = 'Solicitar Alteração Equipe';
                btnRequestTeamChange.onclick = () => openTeamChangeRequestModal(inspId);
                actionButtonsContainer.appendChild(btnRequestTeamChange);


                if (currentInspection.status === "Agendada") {
                    const btnIniciar = document.createElement('button');
                    btnIniciar.className = 'action-button bg-blue-600 hover:bg-blue-700 text-white';
                    btnIniciar.textContent = 'Iniciar Inspeção';
                    btnIniciar.onclick = () => {
                        currentInspection.status = "Em Andamento";
                        renderPageDetails(inspId);
                    };
                    actionButtonsContainer.appendChild(btnIniciar);
                } else if (currentInspection.status === "Em Andamento") {
                    const btnFinalizar = document.createElement('button');
                    btnFinalizar.className = 'action-button bg-red-600 hover:bg-red-700 text-white';
                    btnFinalizar.textContent = 'Finalizar Inspeção';
                    btnFinalizar.onclick = () => {
                        currentInspection.status = "Concluída";
                        renderPageDetails(inspId);
                    };
                    actionButtonsContainer.appendChild(btnFinalizar);

                    const btnRealizarInspecao = document.createElement('button');
                    btnRealizarInspecao.className = 'action-button bg-teal-500 hover:bg-teal-600 text-white';
                    btnRealizarInspecao.textContent = 'Realizar Inspeção';
                    btnRealizarInspecao.onclick = () => {
                        window.location.href = `iniciarInspecao.html?id=${inspId}`;
                    };
                    actionButtonsContainer.appendChild(btnRealizarInspecao);

                } else if (currentInspection.status === "Concluída") {
                    const btnGerarRelatorio = document.createElement('button');
                    btnGerarRelatorio.className = 'action-button bg-blue-600 hover:bg-blue-700 text-white';
                    btnGerarRelatorio.textContent = 'Gerar Relatório';
                    btnGerarRelatorio.onclick = () => {
                        window.location.href = `relatorios.html?id=${inspId}`;
                    };
                    actionButtonsContainer.appendChild(btnGerarRelatorio);
                }

                renderHierarquia(currentInspection.edificios, inspId);

            } else {
                if (layoutContentContainer) {
                    layoutContentContainer.innerHTML = `
                        <div class="text-center p-10">
                            <h2 class="text-2xl font-semibold text-red-500 mb-4">Inspeção Não Encontrada</h2> 
                            <p class="text-gray-600">O ID '${inspId}' não corresponde a nenhuma inspeção conhecida.</p> 
                            <a href="listaInspecoes.html" class="mt-6 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Voltar para Lista de Inspeções
                            </a>
                        </div>
                    `;
                }
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            const urlParams = new URLSearchParams(window.location.search);
            const inspectionIdParam = urlParams.get('id');

            if (inspectionIdParam && inspectionsData[inspectionIdParam]) {
                renderPageDetails(inspectionIdParam);
            } else if (inspectionIdParam && !inspectionsData[inspectionIdParam]) {
                const container = document.querySelector('.layout-content-container');
                if (container) { // Adicionar verificação se o container existe
                    container.innerHTML = `<div class="text-center p-10"><h2 class="text-2xl font-semibold text-red-500 mb-4">Inspeção Não Encontrada</h2><p class="text-gray-600">O ID '${inspectionIdParam}' não existe.</p><a href="listaInspecoes.html" class="mt-6 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Voltar</a></div>`;
                }
            }
            else {
                const firstInspId = Object.keys(inspectionsData)[0];
                if (firstInspId) {
                    const newUrl = `${window.location.pathname}?id=${firstInspId}${window.location.hash}`;
                    window.history.replaceState({}, '', newUrl);
                    renderPageDetails(firstInspId);
                } else {
                    const container = document.querySelector('.layout-content-container');
                    if (container) { // Adicionar verificação se o container existe
                        container.innerHTML = `<div class="text-center p-10"><h2 class="text-2xl font-semibold text-red-500 mb-4">Nenhuma Inspeção Disponível</h2><p class="text-gray-600">Não há dados de inspeção para carregar.</p><a href="listaInspecoes.html" class="mt-6 inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Voltar</a></div>`;
                    }
                }
            }

            const currentPath = window.location.pathname.split('/').pop();
            const navLinks = document.querySelectorAll('header nav a');
            navLinks.forEach(link => {
                const linkPath = link.getAttribute('href').split('/').pop();
                if (linkPath === currentPath || (currentPath === 'detalhesInspecao.html' && linkPath === 'listaInspecoes.html')) {
                    link.classList.add('nav-item-active');
                    const img = link.querySelector('img');
                    if (img) img.classList.add('icon-filter-blue');
                } else {
                    link.classList.remove('nav-item-active');
                    const img = link.querySelector('img');
                    if (img) img.classList.remove('icon-filter-blue');
                }            });
        });

        // ============ FUNÇÕES DE GERENCIAMENTO DE FOTOS ============

        let currentPhotoContext = { type: '', id: '', editingIndex: -1 };

        function openAddPhotoModal(type, id) {
            currentPhotoContext = { type, id, editingIndex: -1 };
            document.getElementById('photoModalTitle').textContent = 'Adicionar Foto';
            document.getElementById('photoSubmitText').textContent = 'Adicionar';
            document.getElementById('photoForm').reset();
            document.getElementById('photoModal').style.display = 'flex';
        }

        function openEditPhotoModal(type, id, index, description) {
            currentPhotoContext = { type, id, editingIndex: index };
            document.getElementById('photoModalTitle').textContent = 'Editar Foto';
            document.getElementById('photoSubmitText').textContent = 'Salvar';
            document.getElementById('photoDescription').value = description || '';
            document.getElementById('photoModal').style.display = 'flex';
        }

        function closePhotoModal() {
            document.getElementById('photoModal').style.display = 'none';
            currentPhotoContext = { type: '', id: '', editingIndex: -1 };
        }

        function removePhoto(type, id, index) {
            if (!confirm('Tem certeza que deseja remover esta foto?')) return;
            
            // Aqui você implementaria a lógica para remover a foto do servidor
            console.log(`Removendo foto: ${type}-${id}-${index}`);
            
            // Simular remoção e recarregar fotos
            loadPhotos(type, id);
            showNotification('Foto removida com sucesso!', 'success');
        }

        function loadPhotos(type, id) {
            const container = document.getElementById(`fotos${type.charAt(0).toUpperCase() + type.slice(1)}Container`);
            const noPhotosMessage = document.getElementById('noPhotosMessage');
            
            // Dados simulados de fotos - em um sistema real, viriam do servidor
            const samplePhotos = [
                { url: '../ícones/disruption.png', description: 'Vista geral da fachada principal' },
                { url: '../ícones/apartamento.png', description: 'Detalhe da estrutura' }
            ];

            if (!container) return;

            container.innerHTML = '';

            if (samplePhotos.length === 0) {
                if (noPhotosMessage) noPhotosMessage.style.display = 'block';
                return;
            }

            if (noPhotosMessage) noPhotosMessage.style.display = 'none';

            samplePhotos.forEach((photo, index) => {
                const photoDiv = document.createElement('div');
                photoDiv.className = 'photo-container relative group';
                photoDiv.innerHTML = `
                    <div class="relative">
                        <img src="${photo.url}" alt="Foto ${index + 1}" class="w-full h-48 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                            <button onclick="openEditPhotoModal('${type}', '${id}', ${index}, '${photo.description}')" 
                                class="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                </svg>
                            </button>
                            <button onclick="removePhoto('${type}', '${id}', ${index})" 
                                class="p-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="3,6 5,6 21,6"/>
                                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 mt-2 px-1">${photo.description}</p>
                `;
                container.appendChild(photoDiv);
            });
        }

        document.getElementById('photoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('photoUpload');
            const description = document.getElementById('photoDescription').value;
            
            if (currentPhotoContext.editingIndex === -1 && !fileInput.files[0]) {
                alert('Por favor, selecione uma foto.');
                return;
            }

            // Aqui você implementaria o upload da foto para o servidor
            console.log('Salvando foto:', {
                type: currentPhotoContext.type,
                id: currentPhotoContext.id,
                file: fileInput.files[0],
                description: description,
                editing: currentPhotoContext.editingIndex !== -1
            });

            // Simular sucesso e recarregar fotos
            loadPhotos(currentPhotoContext.type, currentPhotoContext.id);
            closePhotoModal();
            
            const action = currentPhotoContext.editingIndex === -1 ? 'adicionada' : 'atualizada';
            showNotification(`Foto ${action} com sucesso!`, 'success');
        });

        function showNotification(message, type = 'info') {
            // Implementação simples de notificação - você pode usar uma biblioteca mais sofisticada
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-600' : 
                type === 'error' ? 'bg-red-600' : 'bg-blue-600'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Carregar fotos da inspeção quando a página carregar
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const urlParams = new URLSearchParams(window.location.search);
                const inspectionId = urlParams.get('id') || 'INSP-001';
                loadPhotos('inspecao', inspectionId);
            }, 500);
        });
    </script>
</body>

</html>