<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Inspeção - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }

        body {
            color: #374151;
            /* text-gray-700 */
            background-color: #f9fafb;
            /* bg-gray-50 */
            font-family: "Public Sans", "Noto Sans", sans-serif;
        }

        .header-bg {
            background-color: #ffffff;
            border-bottom: 1px solid #e5e7eb;
        }

        .page-title {
            color: #111827;
            /* text-gray-900 */
        }

        .card-section {
            /* For main sections like Edificios, Pavimentos */
            background-color: #ffffff;
            border-color: #e5e7eb;
            /* border-gray-200 */
            padding: 1.5rem;
            /* p-6 */
            border-radius: 0.5rem;
            /* rounded-lg */
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            /* shadow-md */
            margin-bottom: 1.5rem;
            /* mb-6 */
        }

        .card-section-title {
            color: #1f2937;
            /* text-gray-800 */
            font-size: 1.25rem;
            /* text-xl */
            font-weight: 600;
            /* font-semibold */
        }

        .item-card {
            /* For individual items like a specific Edificio */
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            padding: 1rem;
            /* p-4 */
            border-radius: 0.5rem;
            /* rounded-lg */
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            margin-bottom: 0.75rem;
            /* mb-3 */
        }

        .item-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
            /* mb-2 */
        }

        .item-card-title {
            font-size: 1.125rem;
            /* text-lg */
            font-weight: 500;
            /* font-medium */
            color: #1f2937;
            /* text-gray-800 */
        }

        .item-card-body p {
            font-size: 0.875rem;
            /* text-sm */
            color: #4b5563;
            /* text-gray-600 */
            margin-bottom: 0.25rem;
            /* mb-1 */
        }

        .item-card-body strong {
            color: #374151;
            /* text-gray-700 */
        }

        .item-description-display {
            /* For displaying descriptions within item cards */
            font-size: 0.875rem;
            color: #4b5563;
            margin-top: 0.25rem;
            padding-left: 0.5rem;
            border-left: 2px solid #d1d5db;
        }


        .action-btn {
            padding: 0.5rem 1rem;
            /* py-2 px-4 */
            font-size: 0.875rem;
            /* text-sm */
            font-weight: 500;
            /* font-medium */
            border-radius: 0.375rem;
            /* rounded-md */
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
            border: 1px solid transparent;
        }

        .action-btn:hover {
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .btn-primary {
            background-color: #2563eb;
            /* bg-blue-600 */
            color: white;
        }

        .btn-primary:hover {
            background-color: #1d4ed8;
            /* bg-blue-700 */
        }

        .btn-primary-active {
            /* For selected items */
            background-color: #4f46e5;
            /* bg-indigo-600 */
            color: white;
        }

        .btn-primary-active:hover {
            background-color: #4338ca;
            /* bg-indigo-700 */
        }

        .btn-secondary {
            background-color: #e5e7eb;
            /* bg-gray-200 */
            color: #374151;
            /* text-gray-700 */
            border-color: #d1d5db;
            /* border-gray-300 */
        }

        .btn-secondary:hover {
            background-color: #d1d5db;
            /* bg-gray-300 */
        }

        .btn-success {
            background-color: #10b981;
            /* bg-green-600 */
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
            /* bg-green-700 */
        }

        /* Modal Styling */
        .modal-bg-overlay {
            background-color: rgba(0, 0, 0, 0.6);
            /* bg-black bg-opacity-60 */
        }

        .modal-content-card {
            background-color: #ffffff;
            padding: 1.5rem;
            /* p-6 */
            border-radius: 0.5rem;
            /* rounded-lg */
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            /* shadow-xl */
            width: 100%;
            max-width: 32rem;
            /* max-w-lg */
            margin-left: auto;
            margin-right: auto;
        }

        .modal-title {
            font-size: 1.25rem;
            /* text-lg */
            font-weight: 600;
            /* font-semibold */
            margin-bottom: 1rem;
            /* mb-4 */
            color: #111827;
            /* text-gray-900 */
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            /* text-sm */
            font-weight: 500;
            /* font-medium */
            color: #374151;
            /* text-gray-700 */
            margin-bottom: 0.25rem;
            /* mb-1 */
        }

        .modal-input,
        .modal-textarea,
        .modal-select {
            margin-top: 0.25rem;
            /* mt-1 */
            display: block;
            width: 100%;
            padding: 0.5rem 0.75rem;
            /* px-3 py-2 */
            border: 1px solid #d1d5db;
            /* border-gray-300 */
            border-radius: 0.375rem;
            /* rounded-md */
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            /* shadow-sm */
            font-size: 0.875rem;
            /* sm:text-sm */
            background-color: #f9fafb;
            /* bg-gray-50 */
        }

        .modal-input:focus,
        .modal-textarea:focus,
        .modal-select:focus {
            outline: none;
            border-color: #2563eb;
            /* focus:border-blue-600 */
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
            /* focus:ring-blue-500 focus:ring-opacity-50 */
        }

        .patologia-card {
            /* Specific for patologia items within a sistema */
            background-color: #f9fafb;
            /* bg-gray-50 */
            padding: 0.75rem;
            /* p-3 */
            border-radius: 0.375rem;
            /* rounded-md */
            border: 1px solid #e5e7eb;
            /* border-gray-200 */
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.04);
        }

        .patologia-checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .patologia-checkbox {
            height: 1rem;
            /* h-4 */
            width: 1rem;
            /* w-4 */
            color: #4f46e5;
            /* text-indigo-600 */
            border-color: #d1d5db;
            /* border-gray-300 */
            border-radius: 0.25rem;
            /* rounded */
        }

        .patologia-checkbox:focus {
            ring: #4f46e5;
            /* focus:ring-indigo-500 */
        }

        .patologia-name {
            margin-left: 0.5rem;
            /* ml-2 */
            color: #374151;
            /* text-gray-700 */
        }

        .patologia-description {
            /* For observacoes */
            font-size: 0.75rem;
            /* text-xs */
            color: #6b7280;
            /* text-gray-500 */
            margin-top: 0.25rem;
            /* mt-1 */
            margin-left: 1.5rem;
            /* ml-6, to align under checkbox text */
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden">
        <div class="layout-container flex h-full grow flex-col">
            <header class="header-bg flex items-center justify-between whitespace-nowrap px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-100 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-100 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8 items-center">
                    <div class="flex items-center gap-4" x-data="{ notificationsOpen: false, hasNotifications: true }">
                        <button @click="notificationsOpen = !notificationsOpen" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="notificationsOpen = false"
                            class="absolute top-16 right-16 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Nova inspeção #12350 atribuída a você.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Lembrete: Inspeção #12345 vence amanhã.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="perfil.html">
                        <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                            style="background-image: url('../ícones/perfil.png');">
                        </div>
                    </a>
                </div>
            </header>

            <main class="flex-1" x-data="inspectionApp()">
                <div class="px-4 sm:px-6 md:px-10 lg:px-20 xl:px-40 flex flex-1 justify-center py-5">
                    <div class="layout-content-container flex flex-col w-full max-w-5xl flex-1">
                        <div class="flex flex-wrap justify-between items-center gap-3 p-4 mb-4">
                            <div class="flex items-center gap-2">
                                <a :href="`detalhesInspecao.html?id=${inspectionId}`"
                                    class="text-blue-600 hover:underline p-1.5 rounded-full hover:bg-gray-200">
                                    <img src="../ícones/menu-aberto.png" alt="Voltar" width="24px" height="24px"
                                        style="transform: rotate(180deg);">
                                </a>
                                <p class="page-title tracking-light text-2xl md:text-3xl font-bold leading-tight">
                                    Realizando Inspeção <span x-text="'#' + inspectionId" class="text-gray-500"></span>
                                </p>
                            </div>
                            <button @click="showFinalizarModal = true" class="action-btn btn-success">
                                <span class="truncate">Finalizar Inspeção</span>
                            </button>
                        </div>

                        <!-- Informações da Inspeção (Read-only) -->
                        <div class="card-section">
                            <h3 class="card-section-title mb-4">Informações da Inspeção</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p><strong>Nome:</strong> <span x-text="inspectionDisplayData.name"></span></p>
                                    <p><strong>Endereço:</strong> <span x-text="inspectionDisplayData.address"></span>
                                    </p>
                                </div>
                                <div>
                                    <p><strong>Coordenador:</strong> <span
                                            x-text="inspectionDisplayData.coordinator"></span></p>
                                    <p><strong>Inspetores:</strong> <span
                                            x-text="inspectionDisplayData.inspectors ? inspectionDisplayData.inspectors.join(', ') : 'N/A'"></span>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Gerenciamento de Edifícios -->
                        <div class="card-section">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="card-section-title">Edifícios</h3>
                                <button @click="openItemModal('edificio')"
                                    class="action-btn btn-primary text-sm">Adicionar Edifício</button>
                            </div>
                            <div x-show="!currentInspectionData.edificios || currentInspectionData.edificios.length === 0"
                                class="text-gray-500 text-center py-3">Nenhum edifício cadastrado.</div>
                            <div class="space-y-3">
                                <template x-for="(edificio, index) in currentInspectionData.edificios"
                                    :key="edificio.id">
                                    <div class="item-card">
                                        <div class="item-card-header">
                                            <h4 class="item-card-title" x-text="edificio.nome"></h4>
                                            <div class="flex gap-2">
                                                <button @click="openItemModal('edificio', edificio.id)"
                                                    class="action-btn btn-secondary text-xs">Editar</button>
                                                <button @click="selectEdificio(edificio.id)"
                                                    class="action-btn btn-primary text-xs"
                                                    :class="{'btn-primary-active': selectedEdificioId === edificio.id}">Selecionar</button>
                                            </div>
                                        </div>
                                        <div class="item-card-body">
                                            <p><strong>Tipo:</strong> <span x-text="edificio.tipo || 'N/D'"></span></p>
                                            <p x-show="edificio.descricao" class="item-description-display"
                                                x-text="edificio.descricao"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Gerenciamento de Pavimentos -->
                        <div x-show="selectedEdificio" class="card-section">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="card-section-title">Pavimentos de <span x-text="selectedEdificio?.nome"
                                        class="text-blue-600"></span></h3>
                                <button @click="openItemModal('pavimento')"
                                    class="action-btn btn-primary text-sm">Adicionar Pavimento</button>
                            </div>
                            <div x-show="selectedEdificio && (!selectedEdificio.pavimentos || selectedEdificio.pavimentos.length === 0)"
                                class="text-gray-500 text-center py-3">Nenhum pavimento cadastrado para este edifício.
                            </div>
                            <div class="space-y-3">
                                <template x-for="(pavimento, index) in selectedEdificio?.pavimentos || []" :key="pavimento.id">
                                    <div class="item-card">
                                        <div class="item-card-header">
                                            <h4 class="item-card-title" x-text="pavimento.nome"></h4>
                                            <div class="flex gap-2">
                                                <button @click="openItemModal('pavimento', pavimento.id)"
                                                    class="action-btn btn-secondary text-xs">Editar</button>
                                                <button @click="selectPavimento(pavimento.id)"
                                                    class="action-btn btn-primary text-xs"
                                                    :class="{'btn-primary-active': selectedPavimentoId === pavimento.id}">Selecionar</button>
                                            </div>
                                        </div>
                                        <div class="item-card-body">
                                            <p><strong>Número:</strong> <span
                                                    x-text="pavimento.numero !== undefined ? pavimento.numero : 'N/D'"></span>
                                            </p>
                                            <p x-show="pavimento.descricao" class="item-description-display"
                                                x-text="pavimento.descricao"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Gerenciamento de Ambientes -->
                        <div x-show="selectedPavimento" class="card-section">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="card-section-title">Ambientes de <span x-text="selectedPavimento?.nome"
                                        class="text-blue-600"></span></h3>
                                <button @click="openItemModal('ambiente')"
                                    class="action-btn btn-primary text-sm">Adicionar Ambiente</button>
                            </div>
                            <div x-show="selectedPavimento && (!selectedPavimento.ambientes || selectedPavimento.ambientes.length === 0)"
                                class="text-gray-500 text-center py-3">Nenhum ambiente cadastrado para este pavimento.
                            </div>
                            <div class="space-y-3">
                                <template x-for="(ambiente, index) in selectedPavimento?.ambientes || []" :key="ambiente.id">
                                    <div class="item-card">
                                        <div class="item-card-header">
                                            <h4 class="item-card-title" x-text="ambiente.nome"></h4>
                                            <div class="flex gap-2">
                                                <button @click="openItemModal('ambiente', ambiente.id)"
                                                    class="action-btn btn-secondary text-xs">Editar</button>
                                                <button @click="selectAmbiente(ambiente.id)"
                                                    class="action-btn btn-primary text-xs"
                                                    :class="{'btn-primary-active': selectedAmbienteId === ambiente.id}">Selecionar</button>
                                            </div>
                                        </div>
                                        <div class="item-card-body">
                                            <p><strong>Tipo:</strong> <span x-text="ambiente.tipo || 'N/D'"></span></p>
                                            <p x-show="ambiente.descricao" class="item-description-display"
                                                x-text="ambiente.descricao"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>


                        <!-- Lista de Sistemas e Patologias -->
                        <div x-show="selectedAmbiente" class="card-section">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="card-section-title">Sistemas de <span x-text="selectedAmbiente?.nome"
                                        class="text-blue-600"></span></h3>
                                <button @click="openItemModal('sistema')"
                                    class="action-btn btn-primary text-sm">Adicionar Sistema</button>
                            </div>
                            <div x-show="selectedAmbiente && (!selectedAmbiente.sistemas || selectedAmbiente.sistemas.length === 0)"
                                class="text-gray-500 text-center py-3">Nenhum sistema cadastrado para este ambiente.
                            </div>
                            <div class="space-y-6">
                                <template x-for="(sistema, sistemaIndex) in selectedAmbiente?.sistemas || []"
                                    :key="sistema.id">
                                    <div class="bg-gray-50 p-4 rounded-lg shadow-inner border border-gray-200">
                                        <div class="flex justify-between items-center mb-3">
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-800" x-text="sistema.nome">
                                                </h4>
                                                <p x-show="sistema.descricao"
                                                    class="text-xs text-gray-500 mt-1 item-description-display"
                                                    x-text="sistema.descricao"></p>
                                            </div>
                                            <div class="flex gap-2 items-center">
                                                <button @click="openItemModal('sistema', sistema.id)"
                                                    class="action-btn btn-secondary text-xs">Editar Sistema</button>
                                                <button @click="toggleSistemaConcluido(sistema.id)"
                                                    class="text-xs font-medium py-1 px-3 rounded-full"
                                                    :class="isSistemaCompleted(sistema.id) ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-700'"
                                                    x-text="isSistemaCompleted(sistema.id) ? 'Concluído' : 'Pendente'">
                                                </button>
                                            </div>
                                        </div>

                                        <div class="space-y-3 pl-4 border-l-2 border-gray-300">
                                            <h5 class="text-sm font-semibold text-gray-700 mb-2">Patologias:</h5>
                                            <template x-for="(patologia, patologiaIndex) in sistema.patologias"
                                                :key="patologia.id">
                                                <div class="patologia-card">
                                                    <div class="flex justify-between items-start">
                                                        <div>
                                                            <label :for="'patologia-' + patologia.id"
                                                                class="patologia-checkbox-label">
                                                                <input :id="'patologia-' + patologia.id" type="checkbox"
                                                                    x-model="patologia.concluida"
                                                                    @change="updateSistemaStatus(sistema.id)"
                                                                    class="patologia-checkbox">
                                                                <span class="patologia-name"
                                                                    x-text="patologia.nome_patologia"></span>
                                                            </label>
                                                            <p x-show="patologia.observacoes"
                                                                class="patologia-description"
                                                                x-text="patologia.observacoes"></p>
                                                        </div>
                                                        <button @click="openPatologiaModal(sistema.id, patologia.id)"
                                                            class="action-btn btn-secondary text-xs whitespace-nowrap">
                                                            Detalhes/Fotos
                                                        </button>
                                                    </div>
                                                </div>
                                            </template>
                                            <button @click="openPatologiaModal(sistema.id)"
                                                class="mt-2 action-btn btn-secondary text-xs">
                                                Adicionar Patologia
                                            </button>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- Modal Genérico para Adicionar/Editar Itens -->
                        <!-- !! IMPORTANTE !! -->
                        <!-- As seguintes propriedades Alpine.js DEVEM SER INICIALIZADAS no objeto retornado por inspectionApp(): -->
                        <!-- modalMode: '' -->
                        <!-- currentItemType: '' -->
                        <!-- currentItemTypeTitle: '' -->
                        <!-- currentItemData: {} (como um objeto vazio) -->
                        <div x-show="showItemModal" @click.away="closeItemModal()" x-cloak
                            class="fixed inset-0 modal-bg-overlay overflow-y-auto h-full w-full flex items-center justify-center z-50 p-4">
                            <div @click.stop class="modal-content-card">
                                <h4 class="modal-title" x-text="(modalMode === 'add' ? 'Adicionar ' : 'Editar ') + (currentItemTypeTitle || '')"></h4>
                                <!-- Campos do formulário -->
                                <!-- The following bindings will cause errors if currentItemData is not an object or if currentItemType, modalMode are not defined. -->
                                <!-- Ensure currentItemData is initialized as {} and currentItemType, modalMode, currentItemTypeTitle are initialized in inspectionApp() -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="form-label">Nome:</label>
                                        <input type="text" x-model="currentItemData.nome" class="modal-input">
                                    </div>
                                    <div x-show="currentItemType === 'edificio' || currentItemType === 'ambiente'">
                                        <label class="form-label">Tipo:</label>
                                        <input type="text" x-model="currentItemData.tipo" class="modal-input">
                                    </div>
                                    <div x-show="currentItemType === 'pavimento'">
                                        <label class="form-label">Número:</label>
                                        <input type="number" x-model.number="currentItemData.numero" class="modal-input">
                                    </div>
                                    <div>
                                        <label class="form-label">Descrição (Opcional):</label>
                                        <textarea x-model="currentItemData.descricao" class="modal-textarea"></textarea>
                                    </div>
                                </div>
                                <div class="mt-6 flex justify-end gap-3">
                                    <button @click="closeItemModal()" class="action-btn btn-secondary">Cancelar</button>
                                    <button @click="saveItem()" class="action-btn btn-primary">Salvar</button>
                                </div>
                            </div>
                        </div>


                        <!-- Modal Detalhes/Fotos da Patologia -->
                        <div x-show="showPatologiaModal" @click.away="closePatologiaModal()" x-cloak
                            class="fixed inset-0 modal-bg-overlay overflow-y-auto h-full w-full flex items-center justify-center z-50 p-4">
                            <!-- Wrap content in x-if to ensure currentPatologia is not null -->
                            <template x-if="currentPatologia">
                                <div @click.stop class="modal-content-card max-w-xl">
                                    <h4 class="modal-title">Detalhes da Patologia: <span x-text="currentPatologia.nome_patologia || 'Nova Patologia'"></span></h4>
                                    <div class="space-y-4">
                                        <div>
                                            <label class="form-label">Nome da Patologia:</label>
                                            <input type="text" x-model="currentPatologia.nome_patologia" class="modal-input" placeholder="Ex: Fissura na viga">
                                        </div>
                                        <div>
                                            <label class="form-label">Observações:</label>
                                            <textarea x-model="currentPatologia.observacoes" placeholder="Adicione suas observações aqui..." class="modal-textarea h-24"></textarea>
                                        </div>
                                        <div>
                                            <label class="form-label">Criticidade:</label>
                                            <select x-model="currentPatologia.criticidade" class="modal-select">
                                                <option value="" disabled>Selecione a criticidade</option>
                                                <option value="Baixa">Baixa</option>
                                                <option value="Média">Média</option>
                                                <option value="Alta">Alta</option>
                                                <option value="Urgente">Urgente</option>
                                            </select>
                                        </div>                                        <div>
                                            <label class="form-label">Fotos:</label>
                                            <input type="file" @change="handleFotoUpload($event, currentPatologia)" multiple class="modal-input">
                                            <div class="mt-2 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                                                <template x-for="(foto, index) in currentPatologia.fotos || []" :key="index">
                                                    <div class="relative">
                                                        <img :src="foto.url || foto.preview || foto" alt="Foto da patologia" class="rounded-md object-cover h-24 w-full">
                                                        <button @click="removerFoto(index, currentPatologia)" class="absolute top-1 right-1 bg-red-500 text-white rounded-full p-0.5 text-xs">&times;</button>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-6 flex justify-end gap-3">
                                        <button @click="closePatologiaModal()" class="action-btn btn-secondary">Cancelar</button>
                                        <button @click="savePatologia()" class="action-btn btn-primary">Salvar Patologia</button>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- Modal Finalizar Inspeção -->
                        <div x-show="showFinalizarModal" @click.away="showFinalizarModal = false" x-cloak
                            class="fixed inset-0 modal-bg-overlay overflow-y-auto h-full w-full flex items-center justify-center z-50">
                            <div class="modal-content-card max-w-md">
                                <h3 class="text-xl font-semibold text-center mb-6 page-title">Finalizar Inspeção?</h3>
                                <p class="text-gray-600 text-center mb-8">Tem certeza de que deseja finalizar esta
                                    inspeção? Esta ação não poderá ser desfeita e os dados serão salvos.</p>
                                <div class="flex justify-center gap-4">
                                    <button @click="showFinalizarModal = false"
                                        class="action-btn btn-secondary">Cancelar</button>
                                    <button @click="finalizarInspecao" class="action-btn btn-success">Confirmar e
                                        Salvar</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        const initialInspectionsDataStore = {
            "INSP-001": {
                name: "Inspeção Edifício Alfa", address: "Rua das Palmeiras, 123, Cidade Exemplo", coordinator: "Carlos Silva", inspectors: ["Ana Lima", "Roberto Dias"],
                edificios: [
                    {
                        id: "EDF-001", nome: "Edifício Principal", tipo: "Comercial", descricao: "Bloco A, com 5 andares e subsolo.", pavimentos: [
                            {
                                id: "PAV-E01-01", nome: "Térreo", numero: 0, descricao: "Área de acesso principal e lojas.", ambientes: [
                                    {
                                        id: "AMB-E01-01-01", nome: "Recepção", tipo: "Comum", descricao: "Hall de entrada com balcão de atendimento.", sistemas: [
                                            {
                                                id: "SIS-E01-01-01-01", nome: "Iluminação Central", tipo: "Elétrico", descricao: "Sistema de iluminação da recepção.", patologias: [
                                                    { id: "PAT-001", nome_patologia: "Lâmpada Queimada", observacoes: "Substituir lâmpada LED 20W.", concluida: false, criticidade: "Baixa", fotos: [] }
                                                ]
                                            },
                                            { id: "SIS-E01-01-01-02", nome: "Ar Condicionado Recepção", tipo: "Climatização", descricao: "AC da recepção.", patologias: [], concluido: false }
                                        ], concluido: false
                                    }
                                ]
                            },
                            { id: "PAV-E01-02", nome: "1º Andar", numero: 1, descricao: "Salas comerciais.", ambientes: [] }
                        ]
                    }
                ]
            },
            "INSP-002": { name: "Inspeção Condomínio Beta", address: "Av. dos Bosques, 456", coordinator: "Mariana Costa", inspectors: ["Pedro Alves"], edificios: [] }
        };

        function inspectionApp() {
            return {
                inspectionId: null,
                inspectionDisplayData: { name: 'N/A', address: 'N/A', coordinator: 'N/A', inspectors: [] },
                currentInspectionData: { edificios: [] },

                selectedEdificioId: null,
                selectedPavimentoId: null,
                selectedAmbienteId: null,

                showItemModal: false,
                modalMode: '', // 'add' or 'edit'
                currentItemType: '', // 'edificio', 'pavimento', 'ambiente', 'sistema'
                currentItemTypeTitle: '', // User-friendly title like 'Edifício', 'Pavimento'
                currentItemData: {}, // Holds data for the item being added/edited
                editingItemId: null,

                showPatologiaModal: false,
                currentSistemaIdForPatologia: null,
                currentPatologia: null, // Holds data for the patologia being added/edited
                editingPatologiaId: null,

                showFinalizarModal: false,

                get selectedEdificio() {
                    return this.currentInspectionData.edificios.find(e => e.id === this.selectedEdificioId);
                },
                get selectedPavimento() {
                    if (!this.selectedEdificio) return null;
                    return this.selectedEdificio.pavimentos.find(p => p.id === this.selectedPavimentoId);
                },
                get selectedAmbiente() {
                    if (!this.selectedPavimento) return null;
                    return this.selectedPavimento.ambientes.find(a => a.id === this.selectedAmbienteId);
                },

                init() {
                    const urlParams = new URLSearchParams(window.location.search);
                    this.inspectionId = urlParams.get('id');
                    if (!this.inspectionId) {
                        alert("ID da Inspeção não encontrado na URL.");
                        window.location.href = 'listaInspecoes.html';
                        return;
                    }
                    this.loadInspectionData();
                },

                loadInspectionData() {
                    const storedData = localStorage.getItem(`inspecao_data_${this.inspectionId}`);
                    if (storedData) {
                        this.currentInspectionData = JSON.parse(storedData);
                    } else if (initialInspectionsDataStore[this.inspectionId]) {
                        this.currentInspectionData = JSON.parse(JSON.stringify(initialInspectionsDataStore[this.inspectionId]));
                        this.currentInspectionData.edificios = this.currentInspectionData.edificios || [];
                        this.currentInspectionData.edificios.forEach(e => {
                            e.pavimentos = e.pavimentos || [];
                            e.pavimentos.forEach(p => {
                                p.ambientes = p.ambientes || [];
                                p.ambientes.forEach(a => {
                                    a.sistemas = a.sistemas || [];
                                    a.sistemas.forEach(s => {
                                        s.patologias = s.patologias || [];
                                        s.concluido = s.patologias.length > 0 && s.patologias.every(pat => pat.concluida);
                                    });
                                });
                            });
                        });

                    } else {
                        this.currentInspectionData = { name: 'Nova Inspeção (ID não encontrado no DB inicial)', address: '', coordinator: '', inspectors: [], edificios: [] };
                        alert(`Dados para inspeção ${this.inspectionId} não encontrados. Iniciando com estrutura vazia.`);
                    }
                    this.inspectionDisplayData.name = this.currentInspectionData.name || initialInspectionsDataStore[this.inspectionId]?.name || 'N/A';
                    this.inspectionDisplayData.address = this.currentInspectionData.address || initialInspectionsDataStore[this.inspectionId]?.address || 'N/A';
                    this.inspectionDisplayData.coordinator = this.currentInspectionData.coordenador || initialInspectionsDataStore[this.inspectionId]?.coordinator || 'N/A';
                    this.inspectionDisplayData.inspectors = this.currentInspectionData.inspectors || initialInspectionsDataStore[this.inspectionId]?.inspectors || [];
                    this.saveInspectionDataToLocalStorage();
                },

                saveInspectionDataToLocalStorage() {
                    if (this.inspectionId) {
                        localStorage.setItem(`inspecao_data_${this.inspectionId}`, JSON.stringify(this.currentInspectionData));
                    }
                },

                selectEdificio(edificioId) {
                    this.selectedEdificioId = edificioId;
                    this.selectedPavimentoId = null;
                    this.selectedAmbienteId = null;
                },
                selectPavimento(pavimentoId) {
                    this.selectedPavimentoId = pavimentoId;
                    this.selectedAmbienteId = null;
                },
                selectAmbiente(ambienteId) {
                    this.selectedAmbienteId = ambienteId;
                },

                openItemModal(type, itemId = null) {
                    this.modalMode = itemId ? 'edit' : 'add';
                    this.currentItemType = type;
                    this.editingItemId = itemId;
                    let parentArray;
                    let itemToEdit;

                    switch (type) {
                        case 'edificio':
                            this.currentItemTypeTitle = 'Edifício';
                            parentArray = this.currentInspectionData.edificios;
                            itemToEdit = itemId ? parentArray.find(e => e.id === itemId) : { nome: '', tipo: '', descricao: '', pavimentos: [] };
                            break;
                        case 'pavimento':
                            if (!this.selectedEdificio) { alert('Selecione um edifício primeiro.'); return; }
                            this.currentItemTypeTitle = 'Pavimento';
                            parentArray = this.selectedEdificio.pavimentos;
                            itemToEdit = itemId ? parentArray.find(p => p.id === itemId) : { nome: '', numero: 0, descricao: '', ambientes: [] };
                            break;
                        case 'ambiente':
                            if (!this.selectedPavimento) { alert('Selecione um pavimento primeiro.'); return; }
                            this.currentItemTypeTitle = 'Ambiente';
                            parentArray = this.selectedPavimento.ambientes;
                            itemToEdit = itemId ? parentArray.find(a => a.id === itemId) : { nome: '', tipo: '', descricao: '', sistemas: [] };
                            break;
                        case 'sistema':
                            if (!this.selectedAmbiente) { alert('Selecione um ambiente primeiro.'); return; }
                            this.currentItemTypeTitle = 'Sistema';
                            parentArray = this.selectedAmbiente.sistemas;
                            itemToEdit = itemId ? parentArray.find(s => s.id === itemId) : { nome: '', tipo: '', descricao: '', patologias: [], concluido: false };
                            break;
                        default: return;
                    }
                    this.currentItemData = JSON.parse(JSON.stringify(itemToEdit || {}));
                    this.showItemModal = true;
                },

                closeItemModal() {
                    this.showItemModal = false;
                    this.modalMode = '';
                    this.currentItemType = '';
                    this.currentItemTypeTitle = '';
                    this.currentItemData = {};
                    this.editingItemId = null;
                },

                saveItem() {
                    if (!this.currentItemData.nome || !this.currentItemData.nome.trim()) { alert('O nome é obrigatório.'); return; }

                    let parentArray;
                    let newItem;

                    switch (this.currentItemType) {
                        case 'edificio':
                            parentArray = this.currentInspectionData.edificios;
                            newItem = { ...this.currentItemData, id: this.editingItemId || `edf_${Date.now()}`, pavimentos: this.editingItemId ? this.currentItemData.pavimentos : [] };
                            break;
                        case 'pavimento':
                            parentArray = this.selectedEdificio.pavimentos;
                            newItem = { ...this.currentItemData, id: this.editingItemId || `pav_${Date.now()}`, ambientes: this.editingItemId ? this.currentItemData.ambientes : [] };
                            break;
                        case 'ambiente':
                            parentArray = this.selectedPavimento.ambientes;
                            newItem = { ...this.currentItemData, id: this.editingItemId || `amb_${Date.now()}`, sistemas: this.editingItemId ? this.currentItemData.sistemas : [] };
                            break;
                        case 'sistema':
                            parentArray = this.selectedAmbiente.sistemas;
                            newItem = { ...this.currentItemData, id: this.editingItemId || `sis_${Date.now()}`, patologias: this.editingItemId ? this.currentItemData.patologias : [], concluido: this.editingItemId ? this.currentItemData.concluido : false };
                            break;
                        default: return;
                    }

                    if (this.editingItemId) {
                        const index = parentArray.findIndex(item => item.id === this.editingItemId);
                        if (index !== -1) parentArray.splice(index, 1, newItem);
                    } else {
                        parentArray.push(newItem);
                    }

                    this.saveInspectionDataToLocalStorage();
                    this.closeItemModal();
                },

                openPatologiaModal(sistemaId, patologiaId = null) {
                    this.currentSistemaIdForPatologia = sistemaId;
                    this.editingPatologiaId = patologiaId;
                    const sistema = this.selectedAmbiente.sistemas.find(s => s.id === sistemaId);
                    if (!sistema) return;

                    if (patologiaId) {
                        const patologiaToEdit = sistema.patologias.find(p => p.id === patologiaId);
                        this.currentPatologia = JSON.parse(JSON.stringify(patologiaToEdit));
                    } else {
                        this.currentPatologia = { nome_patologia: '', observacoes: '', concluida: false, criticidade: '', fotos: [] };
                    }
                    this.showPatologiaModal = true;
                },
                closePatologiaModal() {
                    this.showPatologiaModal = false;
                    this.currentPatologia = null;
                    this.editingPatologiaId = null;
                    this.currentSistemaIdForPatologia = null;
                },
                savePatologia() { // Renomeada de savePatologiaDetails
                    if (!this.currentPatologia.nome_patologia || !this.currentPatologia.nome_patologia.trim()) { alert('Nome da patologia é obrigatório.'); return; }
                    const sistema = this.selectedAmbiente.sistemas.find(s => s.id === this.currentSistemaIdForPatologia);
                    if (!sistema) return;

                    const newPatologiaData = JSON.parse(JSON.stringify(this.currentPatologia));

                    if (this.editingPatologiaId) {
                        const index = sistema.patologias.findIndex(p => p.id === this.editingPatologiaId);
                        if (index !== -1) sistema.patologias.splice(index, 1, newPatologiaData);
                    } else {
                        newPatologiaData.id = `pat_${Date.now()}`;
                        sistema.patologias.push(newPatologiaData);
                    }
                    this.updateSistemaStatus(sistema.id);
                    this.saveInspectionDataToLocalStorage();
                    this.closePatologiaModal();
                },

                handleFotoUpload(event, patologiaItem) { // Renomeada de handleModalFileUpload e parâmetro adicionado
                    if (!patologiaItem) return;
                    const files = Array.from(event.target.files);
                    files.forEach(file => {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            if (!patologiaItem.fotos) {
                                patologiaItem.fotos = [];
                            }
                            patologiaItem.fotos.push({ url: e.target.result, name: file.name }); // Usar 'url' para consistência
                        };
                        reader.readAsDataURL(file);
                    });
                    event.target.value = null; // Limpar o input de arquivo
                },
                removerFoto(index, patologiaItem) { // Renomeada de removeModalFoto e parâmetro adicionado
                    if (patologiaItem && patologiaItem.fotos) {
                        patologiaItem.fotos.splice(index, 1);
                    }
                },

                toggleSistemaConcluido(sistemaId) {
                    const sistema = this.selectedAmbiente.sistemas.find(s => s.id === sistemaId);
                    if (sistema) {
                        sistema.concluido = !sistema.concluido;
                        sistema.patologias.forEach(p => p.concluida = sistema.concluido);
                        this.saveInspectionDataToLocalStorage();
                    }
                },
                updateSistemaStatus(sistemaId) {
                    const sistema = this.selectedAmbiente?.sistemas.find(s => s.id === sistemaId);
                    if (sistema) {
                        sistema.concluido = sistema.patologias.length > 0 && sistema.patologias.every(p => p.concluida);
                        this.saveInspectionDataToLocalStorage();
                    }
                },
                isSistemaCompleted(sistemaId) {
                    const sistema = this.selectedAmbiente?.sistemas.find(s => s.id === sistemaId);
                    return sistema ? sistema.concluido : false;
                },

                finalizarInspecao() {
                    this.saveInspectionDataToLocalStorage();
                    console.log("Inspeção finalizada e dados salvos:", this.inspectionId, this.currentInspectionData);
                    alert(`Inspeção ${this.inspectionId} finalizada! Dados salvos localmente.`);
                    localStorage.setItem(`inspecao_status_${this.inspectionId}`, 'Concluída');
                    this.showFinalizarModal = false;
                    window.location.href = `detalhesInspecao.html?id=${this.inspectionId}`;
                }
            };
        }
    </script>
</body>

</html>