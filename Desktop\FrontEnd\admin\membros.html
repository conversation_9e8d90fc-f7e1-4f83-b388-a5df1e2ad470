<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Membros</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }

        .nav-item-active {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            background-color: #eff6ff;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="dashboard.html">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="dashboard.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="membros.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>
            <main class="flex-1">
                <div class="px-6 flex flex-1 justify-center py-5">
                    <div class="layout-content-container flex flex-col w-full max-w-none flex-1">
                        <div class="flex flex-wrap justify-between items-center gap-3 p-4">
                            <div class="flex min-w-72 flex-col gap-3">
                                <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight">Membros</p>
                                <p class="text-[#637688] text-sm font-normal leading-normal">Gerencie os membros e suas
                                    atribuições.</p>
                            </div>
                        </div>
                        <div class="px-4 py-3">
                            <label class="flex flex-col min-w-40 h-12 w-full">
                                <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                                    <div class="text-[#637688] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                                        data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                            fill="currentColor" viewBox="0 0 256 256">
                                            <path
                                                d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                                            </path>
                                        </svg>
                                    </div>
                                    <input placeholder="Buscar membros..."
                                        class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-full placeholder:text-[#637688] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                                        value="">
                                </div>
                            </label>
                        </div>
                        <div class="px-4 py-3 @container">
                            <div class="flex overflow-hidden rounded-xl border border-[#dce1e5] bg-white">
                                <table class="flex-1">
                                    <thead>
                                        <tr class="bg-white">
                                            <th
                                                class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                                Nome</th>
                                            <th
                                                class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                                Cargo</th>
                                            <th
                                                class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                                Inspeções Atribuídas</th>
                                            <th
                                                class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                                Disponibilidade</th>
                                            <th
                                                class="px-4 py-3 text-center text-[#111518] text-sm font-medium leading-normal">
                                                Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="border-t border-t-[#dce1e5]">
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Dra. Mariana Silva
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Inspetora Líder
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                INSP-001
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <button
                                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f6ffed] text-[#52c41a] text-sm font-medium leading-normal w-full mx-auto">
                                                    <span class="truncate">Disponível</span>
                                                </button>
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <a href="detalhesMembro.html?id=1"
                                                    class="text-blue-600 hover:underline">Ver Detalhes</a>
                                            </td>
                                        </tr>
                                        <tr class="border-t border-t-[#dce1e5]">
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Eng. Carlos Santos
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Engenheiro Estrutural
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                INSP-002, INSP-003
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <button
                                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#fff1f0] text-[#f5222d] text-sm font-medium leading-normal w-full mx-auto">
                                                    <span class="truncate">Ocupado</span>
                                                </button>
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <a href="detalhesMembro.html?id=2"
                                                    class="text-blue-600 hover:underline">Ver Detalhes</a>
                                            </td>
                                        </tr>
                                        <tr class="border-t border-t-[#dce1e5]">
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Sra. Ana Pereira
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Especialista Ambiental
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                N/A
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <button
                                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f6ffed] text-[#52c41a] text-sm font-medium leading-normal w-full mx-auto">
                                                    <span class="truncate">Disponível</span>
                                                </button>
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <a href="detalhesMembro.html?id=3"
                                                    class="text-blue-600 hover:underline">Ver Detalhes</a>
                                            </td>
                                        </tr>
                                        <tr class="border-t border-t-[#dce1e5]">
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Sr. João Almeida
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                Oficial de Segurança
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-[#637688] text-sm font-normal leading-normal text-center">
                                                N/A
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <button
                                                    class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f6ffed] text-[#52c41a] text-sm font-medium leading-normal w-full mx-auto">
                                                    <span class="truncate">Disponível</span>
                                                </button>
                                            </td>
                                            <td
                                                class="h-[72px] px-4 py-2 text-sm font-normal leading-normal text-center">
                                                <a href="detalhesMembro.html?id=4"
                                                    class="text-blue-600 hover:underline">Ver Detalhes</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const currentPath = window.location.pathname.split('/').pop();
            if (currentPath === 'dashboard.html') {
                const painelLink = document.querySelector('nav a[href="dashboard.html"]');
                if (painelLink) {
                    painelLink.classList.add('nav-item-active');
                }
            } else if (currentPath === 'dashboard.html') {
                const inspecoesLink = document.querySelector('nav a[href="dashboard.html"]');
                if (inspecoesLink) {
                    inspecoesLink.classList.add('nav-item-active');
                }
            } else if (currentPath === 'relatorios.html') {
                const relatoriosLink = document.querySelector('nav a[href="relatorios.html"]');
                if (relatoriosLink) {
                    relatoriosLink.classList.add('nav-item-active');
                }
            } else if (currentPath === 'membros.html') {
                const membrosLink = document.querySelector('nav a[href="membros.html"]');
                if (membrosLink) {
                    membrosLink.classList.add('nav-item-active');
                }
            }
        });
    </script>
</body>

</html>