<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes do Ambiente - Inspetor</title>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
        .severity-critical { background-color: #fee2e2; color: #991b1b; }
        .severity-high { background-color: #fed7aa; color: #9a3412; }
        .severity-medium { background-color: #fef3c7; color: #92400e; }
        .severity-low { background-color: #dcfce7; color: #166534; }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex items-center gap-2">
                    <button
                        class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900">
                        <img src="../ícones/perfil.png" alt="Perfil" width="32px" height="32px">
                        <span>João Silva</span>
                    </button>
                </div>
            </header>

            <div class="px-40 py-5 flex-1" x-data="environmentDetails()">
                <div class="flex flex-col gap-6">
                    <!-- Header com botão voltar e breadcrumb -->
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" 
                            class="flex items-center gap-2 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="m12 19-7-7 7-7" />
                                <path d="M19 12H5" />
                            </svg>
                            <span class="ml-2">Voltar</span>
                        </button>
                        <div class="flex items-center gap-2 text-sm text-gray-500">
                            <span x-text="building.name"></span>
                            <span>→</span>
                            <span x-text="floor.name"></span>
                            <span>→</span>
                            <span class="text-gray-900 font-medium" x-text="environment.name"></span>
                        </div>
                    </div>

                    <h1 class="text-[#111518] text-2xl font-bold leading-tight" x-text="'Ambiente: ' + environment.name"></h1>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Nome:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Tipo:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Área:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.area + ' m²'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Uso Principal:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.mainUse"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Status da Inspeção</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Status:</span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="{
                                            'bg-green-100 text-green-800': environment.inspectionStatus === 'Completa',
                                            'bg-yellow-100 text-yellow-800': environment.inspectionStatus === 'Em Andamento',
                                            'bg-gray-100 text-gray-800': environment.inspectionStatus === 'Pendente'
                                        }"
                                        x-text="environment.inspectionStatus"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Patologias Encontradas:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.pathologies?.length || 0"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Criticidade Máxima:</span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="{
                                            'severity-critical': environment.maxSeverity === 'Crítica',
                                            'severity-high': environment.maxSeverity === 'Alta',
                                            'severity-medium': environment.maxSeverity === 'Média',
                                            'severity-low': environment.maxSeverity === 'Baixa'
                                        }"
                                        x-text="environment.maxSeverity"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Última Atualização:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.lastUpdate"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Descrição -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrição</h3>
                        <textarea x-model="environment.description" 
                            class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            rows="3"
                            placeholder="Adicione uma descrição do ambiente..."></textarea>
                        <button @click="saveDescription()" 
                            class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                            Salvar Descrição
                        </button>
                    </div>

                    <!-- Fotos do Ambiente -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Fotos do Ambiente</h3>
                            <button @click="showUploadModal = true"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                Adicionar Foto
                            </button>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" x-show="environment.photos && environment.photos.length > 0">
                            <template x-for="photo in environment.photos" :key="photo.id">
                                <div class="relative group">
                                    <img :src="photo.url" :alt="photo.description"
                                        class="w-full h-48 object-cover rounded-lg">
                                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                        <button @click="editPhoto(photo)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                                            </svg>
                                        </button>
                                        <button @click="removePhoto(photo.id)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 6h18" />
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                                <path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-2 px-1" x-text="photo.description"></p>
                                </div>
                            </template>
                        </div>

                        <div x-show="!environment.photos || environment.photos.length === 0" class="text-center py-8 text-gray-500 col-span-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                            <p>Nenhuma foto adicionada ainda</p>
                        </div>
                    </div>

                    <!-- Lista de Patologias -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Patologias Identificadas</h3>
                            <button @click="addPathology()"
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 5v14" />
                                    <path d="M5 12h14" />
                                </svg>
                                Adicionar Patologia
                            </button>
                        </div>
                        
                        <div x-show="!environment.pathologies || environment.pathologies.length === 0" class="text-center py-8 text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p>Nenhuma patologia identificada neste ambiente</p>
                        </div>

                        <div class="space-y-4" x-show="environment.pathologies && environment.pathologies.length > 0">
                            <template x-for="pathology in environment.pathologies" :key="pathology.id">
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-all cursor-pointer"
                                    @click="viewPathologyDetails(pathology.id)">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-3 mb-2">
                                                <h4 class="font-medium text-gray-900" x-text="pathology.name"></h4>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                    :class="{
                                                        'severity-critical': pathology.severity === 'Crítica',
                                                        'severity-high': pathology.severity === 'Alta',
                                                        'severity-medium': pathology.severity === 'Média',
                                                        'severity-low': pathology.severity === 'Baixa'
                                                    }"
                                                    x-text="pathology.severity"></span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2" x-text="pathology.description"></p>
                                            <div class="flex items-center gap-4 text-xs text-gray-500">
                                                <span>Sistema: <span x-text="pathology.system"></span></span>
                                                <span>Data: <span x-text="pathology.date"></span></span>
                                                <span x-show="pathology.photos && pathology.photos.length > 0">
                                                    📷 <span x-text="pathology.photos.length"></span> foto(s)
                                                </span>
                                            </div>
                                        </div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="text-gray-400">
                                            <path d="m9 18 6-6-6-6" />
                                        </svg>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Modal de Upload de Foto -->
                <div x-show="showUploadModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96 max-w-full">
                        <h3 class="text-lg font-semibold mb-4">Adicionar Foto</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Selecionar Arquivo</label>
                                <input type="file" @change="handleFileSelect($event)" accept="image/*" 
                                    class="w-full p-2 border border-gray-300 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                                <textarea x-model="newPhoto.description" 
                                    class="w-full p-2 border border-gray-300 rounded-lg resize-none"
                                    rows="3" placeholder="Descreva esta foto..."></textarea>
                            </div>
                        </div>
                        <div class="flex justify-end gap-2 mt-6">
                            <button @click="closePhotoModal()" 
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button @click="savePhoto()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                Adicionar
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Modal de Edição da Foto -->
                <div x-show="showPhotoEditModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96 max-w-full">
                        <h3 class="text-lg font-semibold mb-4">Editar Descrição da Foto</h3>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                            <textarea x-model="editingPhoto.description" 
                                class="w-full p-2 border border-gray-300 rounded-lg resize-none"
                                rows="3"></textarea>
                        </div>
                        <div class="flex justify-end gap-2 mt-6">
                            <button @click="closePhotoEditModal()" 
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button @click="savePhotoEdit()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                Salvar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function environmentDetails() {
            return {
                building: {},
                floor: {},
                environment: {},
                showUploadModal: false,
                showPhotoEditModal: false,
                newPhoto: {
                    file: null,
                    description: ''
                },
                editingPhoto: {
                    id: null,
                    description: ''
                },

                init() {
                    this.loadEnvironment();
                },

                loadEnvironment() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const environmentId = urlParams.get('id');
                    const floorId = urlParams.get('floorId');
                    const buildingId = urlParams.get('buildingId');

                    // Dados de exemplo do ambiente
                    this.building = {
                        id: buildingId || 'ED-001',
                        name: 'Edifício Sede'
                    };

                    this.floor = {
                        id: floorId || 'FL-001',
                        name: 'Térreo'
                    };

                    this.environment = {
                        id: environmentId || 'ENV-001',
                        name: 'Recepção Principal',
                        type: 'Área Comum',
                        area: 45,
                        mainUse: 'Atendimento ao público',
                        inspectionStatus: 'Completa',
                        maxSeverity: 'Média',
                        lastUpdate: '15/03/2024',
                        description: 'Área de recepção principal do edifício, com balcão de atendimento, área de espera e sistema de climatização central.',
                        photos: [
                            {
                                id: 1,
                                url: '../ícones/disruption.png',
                                description: 'Vista geral da recepção'
                            },
                            {
                                id: 2,
                                url: '../ícones/apartamento.png',
                                description: 'Balcão de atendimento'
                            }
                        ],
                        pathologies: [
                            {
                                id: 1,
                                name: 'Infiltração na parede norte',
                                description: 'Mancha de umidade visível na parede próxima à entrada principal',
                                severity: 'Média',
                                system: 'Estrutural',
                                date: '10/03/2024',
                                photos: ['photo1.jpg', 'photo2.jpg']
                            }
                        ]
                    };
                },

                saveDescription() {
                    alert('Descrição salva com sucesso!');
                },

                viewPathologyDetails(pathologyId) {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesPatologia.html?id=${pathologyId}&environmentId=${this.environment.id}&floorId=${this.floor.id}&buildingId=${this.building.id}&inspectionId=${inspectionId}`;
                },

                addPathology() {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesPatologia.html?new=true&environmentId=${this.environment.id}&floorId=${this.floor.id}&buildingId=${this.building.id}&inspectionId=${inspectionId}`;
                },

                handleFileSelect(event) {
                    this.newPhoto.file = event.target.files[0];
                },

                closePhotoModal() {
                    this.showUploadModal = false;
                    this.newPhoto = { file: null, description: '' };
                },

                savePhoto() {
                    if (!this.newPhoto.file) {
                        alert('Por favor, selecione um arquivo.');
                        return;
                    }
                    
                    const newPhoto = {
                        id: Date.now(),
                        url: '../ícones/disruption.png',
                        description: this.newPhoto.description || 'Sem descrição'
                    };
                    
                    if (!this.environment.photos) {
                        this.environment.photos = [];
                    }
                    this.environment.photos.push(newPhoto);
                    this.closePhotoModal();
                    alert('Foto adicionada com sucesso!');
                },

                editPhoto(photo) {
                    this.editingPhoto = {
                        id: photo.id,
                        description: photo.description
                    };
                    this.showPhotoEditModal = true;
                },

                closePhotoEditModal() {
                    this.showPhotoEditModal = false;
                    this.editingPhoto = { id: null, description: '' };
                },

                savePhotoEdit() {
                    const photoIndex = this.environment.photos.findIndex(p => p.id === this.editingPhoto.id);
                    if (photoIndex !== -1) {
                        this.environment.photos[photoIndex].description = this.editingPhoto.description;
                    }
                    this.closePhotoEditModal();
                    alert('Descrição atualizada com sucesso!');
                },

                removePhoto(photoId) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.environment.photos = this.environment.photos.filter(photo => photo.id !== photoId);
                        alert('Foto removida com sucesso!');
                    }
                }
            }
        }
    </script>
</body>

</html>
