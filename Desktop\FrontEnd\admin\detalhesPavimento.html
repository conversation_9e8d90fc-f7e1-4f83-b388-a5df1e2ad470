<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes do Pavimento</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="dashboard.html">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="dashboard.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="membros.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="floorDetails">
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <div class="flex items-center gap-3">
                                <button id="backButton" onclick="goBack()"
                                    class="flex items-center justify-center rounded-lg h-10 bg-[#f0f2f4] px-3 text-[#111518] hover:bg-gray-300 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m12 19-7-7 7-7" />
                                        <path d="M19 12H5" />
                                    </svg>
                                    <span class="ml-2">Voltar</span>
                                </button>
                                <p class="text-[#111518] text-[32px] font-bold leading-tight tracking-[-0.015em]"
                                    x-text="'Pavimento ' + floor.id"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Navegação Breadcrumb -->
                    <div class="px-4 py-2">
                        <nav class="flex" aria-label="Breadcrumb">
                            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                <li class="inline-flex items-center">
                                    <a href="#" @click="viewBuildingDetails(floor.buildingId)"
                                        class="inline-flex items-center text-sm font-medium text-blue-700 hover:text-blue-600">
                                        <svg class="w-3 h-3 mr-2.5" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                                        </svg>
                                        <span x-text="floor.buildingName"></span>
                                    </a>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 9 4-4-4-4" />
                                        </svg>
                                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2"
                                            x-text="floor.name"></span>
                                    </div>
                                </li>
                            </ol>
                        </nav>
                    </div> <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Informações Básicas</h3>
                            <div class="space-y-2">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Nome:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Edifício:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.buildingName"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Andar:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.level"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Tipo:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.type"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Características</h3>
                            <div class="space-y-2">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Área Total:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.area + ' m²'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Pé Direito:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.ceilingHeight + ' m'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Status:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(floor.status)" x-text="floor.status"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Número de Ambientes:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="floor.environments.length"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200 col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Descrição</h3>
                            <p class="text-sm text-gray-700" x-text="floor.description"></p>
                        </div>
                    </div>

                    <!-- Especificações Técnicas -->
                    <div class="px-4 py-4" x-show="floor.technicalSpecs">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Especificações Técnicas</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div x-show="floor.technicalSpecs.structure">
                                    <span class="text-sm font-medium text-gray-500">Estrutura:</span>
                                    <span class="ml-2 text-sm text-gray-900"
                                        x-text="floor.technicalSpecs.structure"></span>
                                </div>
                                <div x-show="floor.technicalSpecs.flooring">
                                    <span class="text-sm font-medium text-gray-500">Piso:</span>
                                    <span class="ml-2 text-sm text-gray-900"
                                        x-text="floor.technicalSpecs.flooring"></span>
                                </div>
                                <div x-show="floor.technicalSpecs.fireProtection">
                                    <span class="text-sm font-medium text-gray-500">Proteção contra Incêndio:</span>
                                    <span class="ml-2 text-sm text-gray-900"
                                        x-text="floor.technicalSpecs.fireProtection"></span>
                                </div>
                                <div x-show="floor.technicalSpecs.accessibility">
                                    <span class="text-sm font-medium text-gray-500">Acessibilidade:</span>
                                    <span class="ml-2 text-sm text-gray-900"
                                        x-text="floor.technicalSpecs.accessibility"></span>
                                </div>
                            </div>
                        </div>
                    </div> <!-- Fotos do Pavimento -->
                    <div class="px-4 py-4" x-show="floor.photos && floor.photos.length > 0">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Fotos do Pavimento</h3>
                            <div class="grid grid-cols-3 gap-4">
                                <template x-for="photo in floor.photos" :key="photo.id">
                                    <div class="relative">
                                        <img :src="photo.url" :alt="photo.description"
                                            class="w-full h-32 object-cover rounded-lg">
                                        <div class="mt-2">
                                            <p class="text-xs text-gray-600" x-text="photo.description"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de Ambientes -->
                    <div class="flex flex-col gap-3 p-4">
                        <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">Ambientes</h3>
                        <template x-for="environment in floor.environments" :key="environment.id">
                            <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50"
                                @click="viewEnvironmentDetails(environment.id)">
                                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg w-14 h-14"
                                    :style="`background-image: url('${environment.photo || '../ícones/apartamento.png'}')`">
                                </div>
                                <div class="flex flex-col justify-center flex-1">
                                    <p class="text-[#111518] text-base font-medium leading-normal"
                                        x-text="environment.name"></p>
                                    <p class="text-[#637588] text-sm font-normal leading-normal"
                                        x-text="environment.type + ' - ' + environment.area + ' m²'"></p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span
                                        class="text-sm inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(environment.status)" x-text="environment.status"></span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m9 18 6-6-6-6" />
                                    </svg>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function floorDetails() {
            return {
                floor: {},
                isViewMode: false,
                inspectionId: null,
                edificioId: null,

                init() {
                    this.checkViewMode();
                    this.loadFloor();
                },

                checkViewMode() {
                    const urlParams = new URLSearchParams(window.location.search);
                    this.isViewMode = urlParams.get('mode') === 'view';
                    this.inspectionId = urlParams.get('id');
                    this.edificioId = urlParams.get('edificio');

                    if (this.isViewMode) {
                        // Ocultar botões de edição
                        const editActions = document.getElementById('editActions');
                        if (editActions) {
                            editActions.style.display = 'none';
                        }
                    }
                },
                loadFloor() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const floorId = urlParams.get('pavimento') || urlParams.get('id');

                    // Dados de exemplo do pavimento
                    this.floor = {
                        id: floorId || 'PAV-001',
                        name: 'Pavimento Térreo',
                        buildingId: this.edificioId || 'ED-001',
                        buildingName: 'Edifício Principal',
                        level: '0 (Térreo)',
                        type: 'Pavimento Térreo',
                        area: 1200,
                        ceilingHeight: 3.5,
                        status: 'Ativo',
                        description: 'Pavimento térreo com recepção principal, salas comerciais e área de estacionamento coberto.',
                        technicalSpecs: {
                            structure: 'Concreto armado com vigas e pilares',
                            flooring: 'Porcelanato polido 60x60cm nas áreas sociais, carpete nas salas',
                            fireProtection: 'Sistema de sprinklers e hidrantes',
                            accessibility: 'Rampas de acesso e elevador adaptado'
                        },
                        photos: [
                            {
                                id: 1,
                                url: '../ícones/disruption.png',
                                description: 'Vista geral do térreo'
                            },
                            {
                                id: 2,
                                url: '../ícones/disruption.png',
                                description: 'Área de recepção'
                            },
                            {
                                id: 3,
                                url: '../ícones/disruption.png',
                                description: 'Estacionamento'
                            }
                        ],
                        environments: [
                            {
                                id: 'amb1',
                                name: 'Recepção',
                                type: 'Sala de Atendimento',
                                area: 120,
                                status: 'Ativo',
                                photo: '../ícones/apartamento.png'
                            },
                            {
                                id: 'amb2',
                                name: 'Sala Comercial 01',
                                type: 'Escritório',
                                area: 80,
                                status: 'Ativo',
                                photo: '../ícones/apartamento.png'
                            },
                            {
                                id: 'amb3',
                                name: 'Sala Comercial 02',
                                type: 'Sala de Reuniões',
                                area: 100,
                                status: 'Ativo',
                                photo: '../ícones/apartamento.png'
                            },
                            {
                                id: 'amb4',
                                name: 'Estacionamento',
                                type: 'Área de Apoio',
                                area: 900,
                                status: 'Ativo',
                                photo: '../ícones/apartamento.png'
                            }
                        ]
                    };
                },

                getStatusColor(status) {
                    switch (status) {
                        case 'Ativo':
                            return 'bg-green-100 text-green-800';
                        case 'Em Manutenção':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Inativo':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                viewEnvironmentDetails(environmentId) {
                    if (this.isViewMode) {
                        window.location.href = `detalhesAmbiente.html?mode=view&from=inspection&id=${this.inspectionId}&edificio=${this.edificioId}&pavimento=${this.floor.id}&ambiente=${environmentId}`;
                    } else {
                        window.location.href = `detalhesAmbiente.html?id=${environmentId}`;
                    }
                },

                viewBuildingDetails(buildingId) {
                    window.location.href = `detalhesEdificio.html?id=${buildingId}`;
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const fromInspection = urlParams.get('from') === 'inspection';
            const inspectionId = urlParams.get('id');
            const edificioId = urlParams.get('edificio');

            if (fromInspection && inspectionId && edificioId) {
                window.location.href = `detalhesEdificio.html?mode=view&from=inspection&id=${inspectionId}&edificio=${edificioId}`;
            } else {
                window.location.href = 'dashboard.html';
            }
        }
    </script>
</body>

</html>