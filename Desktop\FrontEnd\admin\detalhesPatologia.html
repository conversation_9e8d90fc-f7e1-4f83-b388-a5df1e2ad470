<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes da Patologia</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="dashboard.html">
                        <h2 class="text-[#111518] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>
                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="dashboard.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/botao-de-inicio.png" alt="Painel" width="20px" height="20px">
                            Painel
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                        <a href="membros.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/grupo.png" alt="Membros" width="20px" height="20px">
                            Membros
                        </a>
                    </div>
                </nav>
                <div class="flex justify-end gap-8">
                    <div class="flex items-center gap-4"
                        x-data="{ notificationsOpen: false, hasNotifications: true, toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, closeNotifications() { this.notificationsOpen = false; } }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-0 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Inspeção #12350 atribuída à Equipe X.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Relatório da inspeção #12345 pronto.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                        style="background-image: url('../ícones/perfil.png');">
                    </div>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="pathologyDetails">
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <div class="flex items-center gap-3">
                                <button id="backButton" onclick="goBack()"
                                    class="flex items-center justify-center rounded-lg h-10 bg-[#f0f2f4] px-3 text-[#111518] hover:bg-gray-300 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m12 19-7-7 7-7" />
                                        <path d="M19 12H5" />
                                    </svg>
                                    <span class="ml-2">Voltar</span>
                                </button>
                                <p class="text-[#111518] text-[32px] font-bold leading-tight tracking-[-0.015em]"
                                    x-text="'Patologia ' + pathology.id"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Navegação Breadcrumb -->
                    <div class="px-4 py-2">
                        <nav class="flex" aria-label="Breadcrumb">
                            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                <li class="inline-flex items-center">
                                    <a href="#" @click="viewBuildingDetails(pathology.buildingId)"
                                        class="inline-flex items-center text-sm font-medium text-blue-700 hover:text-blue-600">
                                        <svg class="w-3 h-3 mr-2.5" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z" />
                                        </svg>
                                        <span x-text="pathology.buildingName"></span>
                                    </a>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 9 4-4-4-4" />
                                        </svg>
                                        <a href="#" @click="viewFloorDetails(pathology.floorId)"
                                            class="text-sm font-medium text-blue-700 hover:text-blue-600"
                                            x-text="pathology.floorName"></a>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 9 4-4-4-4" />
                                        </svg>
                                        <a href="#" @click="viewEnvironmentDetails(pathology.environmentId)"
                                            class="text-sm font-medium text-blue-700 hover:text-blue-600"
                                            x-text="pathology.environmentName"></a>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 9 4-4-4-4" />
                                        </svg>
                                        <a href="#" @click="viewSystemDetails(pathology.systemId)"
                                            class="text-sm font-medium text-blue-700 hover:text-blue-600"
                                            x-text="pathology.systemName"></a>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" d="m1 9 4-4-4-4" />
                                        </svg>
                                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2"
                                            x-text="pathology.name"></span>
                                    </div>
                                </li>
                            </ol>
                        </nav>
                    </div>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Informações Básicas</h3>
                            <div class="space-y-2">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Nome:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="pathology.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Sistema:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="pathology.systemName"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Tipo:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="pathology.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Categoria:</span>
                                    <span class="ml-2 text-sm text-gray-900" x-text="pathology.category"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Classificação</h3>
                            <div class="space-y-2">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Severidade:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getSeverityColor(pathology.severity)"
                                        x-text="pathology.severity"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Urgência:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getUrgencyColor(pathology.urgency)" x-text="pathology.urgency"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Status:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(pathology.status)" x-text="pathology.status"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Data de Identificação:</span>
                                    <span class="ml-2 text-sm text-gray-900"
                                        x-text="pathology.identificationDate"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200 col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Descrição</h3>
                            <p class="text-sm text-gray-700" x-text="pathology.description"></p>
                        </div>
                    </div>

                    <!-- Causas e Consequências -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Possíveis Causas</h3>
                            <ul class="space-y-1">
                                <template x-for="cause in pathology.causes" :key="cause">
                                    <li class="text-sm text-gray-700 flex items-start">
                                        <span
                                            class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                        <span x-text="cause"></span>
                                    </li>
                                </template>
                            </ul>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Consequências</h3>
                            <ul class="space-y-1">
                                <template x-for="consequence in pathology.consequences" :key="consequence">
                                    <li class="text-sm text-gray-700 flex items-start">
                                        <span
                                            class="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                        <span x-text="consequence"></span>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div>

                    <!-- Recomendações -->
                    <div class="px-4 py-4">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Recomendações de Intervenção</h3>
                            <div class="space-y-3">
                                <template x-for="recommendation in pathology.recommendations" :key="recommendation.id">
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <h4 class="text-sm font-medium text-gray-900" x-text="recommendation.title">
                                        </h4>
                                        <p class="text-sm text-gray-600 mt-1" x-text="recommendation.description"></p>
                                        <div class="flex gap-4 mt-2">
                                            <span class="text-xs text-gray-500">Prazo: <span
                                                    x-text="recommendation.deadline"></span></span>
                                            <span class="text-xs text-gray-500">Custo estimado: <span
                                                    x-text="recommendation.estimatedCost"></span></span>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Fotos da Patologia -->
                    <div class="px-4 py-4" x-show="pathology.photos && pathology.photos.length > 0">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Fotos da Patologia</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <template x-for="photo in pathology.photos" :key="photo.id">
                                    <div class="relative">
                                        <img :src="photo.url" :alt="photo.description"
                                            class="w-full h-48 object-cover rounded-lg cursor-pointer"
                                            @click="openPhotoModal(photo)">
                                        <div class="mt-2">
                                            <p class="text-xs text-gray-600" x-text="photo.description"></p>
                                            <p class="text-xs text-gray-500" x-text="'Data: ' + photo.date"></p>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Histórico de Ações -->
                    <div class="px-4 py-4" x-show="pathology.actionHistory && pathology.actionHistory.length > 0">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Histórico de Ações</h3>
                            <div class="space-y-3">
                                <template x-for="action in pathology.actionHistory" :key="action.id">
                                    <div class="flex items-start gap-3 pb-3 border-b border-gray-100 last:border-b-0">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900" x-text="action.description">
                                            </p>
                                            <div class="flex gap-4 mt-1">
                                                <span class="text-xs text-gray-500"
                                                    x-text="'Por: ' + action.responsible"></span>
                                                <span class="text-xs text-gray-500"
                                                    x-text="'Data: ' + action.date"></span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Foto -->
    <div id="photoModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50" x-show="showPhotoModal" x-cloak>
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="relative">
                <img :src="selectedPhoto?.url" :alt="selectedPhoto?.description"
                    class="max-w-full max-h-[80vh] object-contain">
                <button @click="showPhotoModal = false"
                    class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
                <div class="absolute bottom-4 left-4 right-4 text-white bg-black bg-opacity-50 p-3 rounded">
                    <p class="text-sm" x-text="selectedPhoto?.description"></p>
                    <p class="text-xs mt-1" x-text="'Data: ' + selectedPhoto?.date"></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function pathologyDetails() {
            return {
                showPhotoModal: false,
                pathology: {},
                selectedPhoto: null,
                init() {
                    this.loadPathology();
                    this.checkViewMode();
                },

                loadPathology() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const pathologyId = urlParams.get('id');

                    // Dados de exemplo da patologia
                    this.pathology = {
                        id: pathologyId || 'PAT-001',
                        name: 'Oxidação nos Conectores',
                        buildingId: 'ED-001',
                        buildingName: 'Edifício Sede',
                        floorId: 'PAV-001',
                        floorName: 'Térreo',
                        environmentId: 'AMB-001',
                        environmentName: 'Recepção',
                        systemId: 'SIS-001',
                        systemName: 'Sistema Elétrico',
                        type: 'Elétrica',
                        category: 'Corrosão',
                        severity: 'Média',
                        urgency: 'Média',
                        status: 'Em Análise',
                        identificationDate: '2024-03-10',
                        description: 'Presença de oxidação visível nos conectores do quadro de distribuição principal, afetando a qualidade das conexões elétricas e podendo comprometer a segurança do sistema.',
                        causes: [
                            'Exposição à umidade do ambiente',
                            'Falta de proteção adequada nos conectores',
                            'Ventilação insuficiente no local',
                            'Qualidade inferior dos materiais utilizados'
                        ],
                        consequences: [
                            'Redução da vida útil dos componentes',
                            'Risco de falha no fornecimento de energia',
                            'Possibilidade de superaquecimento',
                            'Comprometimento da segurança elétrica'
                        ],
                        recommendations: [
                            {
                                id: 1,
                                title: 'Limpeza e Tratamento Imediato',
                                description: 'Realizar limpeza completa dos conectores e aplicação de produto anticorrosivo',
                                deadline: '15 dias',
                                estimatedCost: 'R$ 500,00'
                            },
                            {
                                id: 2,
                                title: 'Melhoria da Ventilação',
                                description: 'Instalar sistema de ventilação forçada no ambiente do quadro elétrico',
                                deadline: '30 dias',
                                estimatedCost: 'R$ 2.500,00'
                            },
                            {
                                id: 3,
                                title: 'Substituição de Componentes',
                                description: 'Substituir conectores danificados por versões com melhor resistência à corrosão',
                                deadline: '45 dias',
                                estimatedCost: 'R$ 1.200,00'
                            }
                        ],
                        photos: [
                            {
                                id: 1,
                                url: '../ícones/disruption.png',
                                description: 'Vista geral da oxidação nos conectores',
                                date: '2024-03-10'
                            },
                            {
                                id: 2,
                                url: '../ícones/disruption.png',
                                description: 'Detalhe da corrosão no conector principal',
                                date: '2024-03-10'
                            },
                            {
                                id: 3,
                                url: '../ícones/disruption.png',
                                description: 'Comparativo com conector não afetado',
                                date: '2024-03-12'
                            },
                            {
                                id: 4,
                                url: '../ícones/disruption.png',
                                description: 'Ambiente do quadro elétrico',
                                date: '2024-03-12'
                            }
                        ],
                        actionHistory: [
                            {
                                id: 1,
                                description: 'Patologia identificada durante inspeção de rotina',
                                responsible: 'João Silva',
                                date: '2024-03-10'
                            },
                            {
                                id: 2,
                                description: 'Documentação fotográfica realizada',
                                responsible: 'Maria Santos',
                                date: '2024-03-10'
                            },
                            {
                                id: 3,
                                description: 'Análise técnica inicial concluída',
                                responsible: 'Carlos Oliveira',
                                date: '2024-03-12'
                            },
                            {
                                id: 4,
                                description: 'Orçamentos solicitados para correção',
                                responsible: 'Ana Costa',
                                date: '2024-03-15'
                            }
                        ]
                    };
                },

                getSeverityColor(severity) {
                    switch (severity) {
                        case 'Baixa':
                            return 'bg-green-100 text-green-800';
                        case 'Média':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Alta':
                            return 'bg-orange-100 text-orange-800';
                        case 'Crítica':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                getUrgencyColor(urgency) {
                    switch (urgency) {
                        case 'Baixa':
                            return 'bg-green-100 text-green-800';
                        case 'Média':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Alta':
                            return 'bg-orange-100 text-orange-800';
                        case 'Imediata':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                getStatusColor(status) {
                    switch (status) {
                        case 'Identificada':
                            return 'bg-blue-100 text-blue-800';
                        case 'Em Análise':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Em Correção':
                            return 'bg-orange-100 text-orange-800';
                        case 'Corrigida':
                            return 'bg-green-100 text-green-800';
                        case 'Monitoramento':
                            return 'bg-purple-100 text-purple-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },
                openPhotoModal(photo) {
                    this.selectedPhoto = photo;
                    this.showPhotoModal = true;
                },

                checkViewMode() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const mode = urlParams.get('mode');

                    if (mode === 'view') {
                        const editActions = document.getElementById('editActions');
                        if (editActions) {
                            editActions.style.display = 'none';
                        }
                    }
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');
            const mode = urlParams.get('mode');

            if (from === 'inspection' && mode === 'view') {
                window.location.href = `detalhesInspecao.html?id=${inspectionId}`;
            } else {
                const systemId = urlParams.get('sistema');
                if (systemId) {
                    if (mode === 'view') {
                        const buildingId = urlParams.get('edificio');
                        const floorId = urlParams.get('pavimento');
                        const environmentId = urlParams.get('ambiente');
                        window.location.href = `detalhesSistema.html?id=${systemId}&mode=view&from=inspection&inspectionId=${inspectionId}&edificio=${buildingId}&pavimento=${floorId}&ambiente=${environmentId}`;
                    } else {
                        window.location.href = `detalhesSistema.html?id=${systemId}`;
                    }
                } else {
                    window.location.href = 'dashboard.html';
                }
            }
        } function generateReport() {
            alert('Funcionalidade de geração de relatório será implementada em breve!');
        } function viewBuildingDetails(buildingId) {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');

            if (mode === 'view' && from === 'inspection') {
                window.location.href = `detalhesEdificio.html?id=${buildingId}&mode=view&from=inspection&inspectionId=${inspectionId}`;
            } else {
                window.location.href = `detalhesEdificio.html?id=${buildingId}`;
            }
        }

        function viewFloorDetails(floorId) {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');
            const buildingId = urlParams.get('edificio');

            if (mode === 'view' && from === 'inspection') {
                window.location.href = `detalhesPavimento.html?id=${floorId}&mode=view&from=inspection&inspectionId=${inspectionId}&edificio=${buildingId}`;
            } else {
                window.location.href = `detalhesPavimento.html?id=${floorId}`;
            }
        }

        function viewEnvironmentDetails(environmentId) {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');
            const buildingId = urlParams.get('edificio');
            const floorId = urlParams.get('pavimento');

            if (mode === 'view' && from === 'inspection') {
                window.location.href = `detalhesAmbiente.html?id=${environmentId}&mode=view&from=inspection&inspectionId=${inspectionId}&edificio=${buildingId}&pavimento=${floorId}`;
            } else {
                window.location.href = `detalhesAmbiente.html?id=${environmentId}`;
            }
        }

        function viewSystemDetails(systemId) {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');
            const buildingId = urlParams.get('edificio');
            const floorId = urlParams.get('pavimento');
            const environmentId = urlParams.get('ambiente');

            if (mode === 'view' && from === 'inspection') {
                window.location.href = `detalhesSistema.html?id=${systemId}&mode=view&from=inspection&inspectionId=${inspectionId}&edificio=${buildingId}&pavimento=${floorId}&ambiente=${environmentId}`;
            } else {
                window.location.href = `detalhesSistema.html?id=${systemId}`;
            }
        }
    </script>
</body>

</html>