<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Edifício - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }
        
        .form-input {
            background-color: #f9fafb;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            padding: 0.5rem 0.75rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-input:disabled {
            background-color: #e5e7eb;
            color: #6b7280;
            cursor: not-allowed;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: background-color 0.15s ease-in-out;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-secondary {
            background-color: #6b7280;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: background-color 0.15s ease-in-out;
        }
        
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        
        .photo-container {
            position: relative;
            overflow: hidden;
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .photo-container img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .photo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s ease-in-out;
        }
        
        .photo-container:hover .photo-overlay {
            opacity: 1;
        }
    </style>
</head>

<body x-data="{ notificationsOpen: false, user: { avatar: '../ícones/perfil.png'} }" class="text-gray-700">
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex items-center gap-2">
                    <button
                        class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900">
                        <img src="../ícones/perfil.png" alt="Perfil" width="32px" height="32px">
                        <span>João Silva</span>
                    </button>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1" x-data="buildingDetails">
                    <div class="flex flex-wrap justify-between gap-3 p-4">
                        <div class="flex min-w-72 flex-col gap-3">
                            <div class="flex items-center gap-3">
                                <button id="backButton" onclick="goBack()"
                                    class="flex items-center justify-center rounded-lg h-10 bg-[#f0f2f4] px-3 text-[#111518] hover:bg-gray-300 transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m12 19-7-7 7-7" />
                                        <path d="M19 12H5" />
                                    </svg>
                                    <span class="ml-2">Voltar</span>
                                </button>
                                <h1 class="text-[#111518] text-2xl font-bold leading-tight tracking-[-0.015em]"
                                    x-text="building.name || 'Carregando...'"></h1>
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button @click="saveBuilding()" 
                                class="btn-primary flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
                                    <polyline points="17,21 17,13 7,13 7,21"/>
                                    <polyline points="7,3 7,8 15,8"/>
                                </svg>
                                Salvar
                            </button>
                        </div>
                    </div>

                    <!-- Informações Básicas Editáveis -->
                    <div class="grid grid-cols-2 gap-4 px-4 py-4">
                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Informações Básicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Nome:</label>
                                    <input type="text" x-model="building.name" 
                                        class="form-input w-full" placeholder="Nome do edifício">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Endereço:</label>
                                    <input type="text" x-model="building.address" 
                                        class="form-input w-full" placeholder="Endereço completo">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Tipo:</label>
                                    <select x-model="building.type" class="form-input w-full">
                                        <option value="">Selecione o tipo</option>
                                        <option value="Comercial">Comercial</option>
                                        <option value="Residencial">Residencial</option>
                                        <option value="Industrial">Industrial</option>
                                        <option value="Misto">Misto</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Ano de Construção:</label>
                                    <input type="number" x-model="building.constructionYear" 
                                        class="form-input w-full" placeholder="Ex: 2015">
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Especificações Técnicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Área Total (m²):</label>
                                    <input type="number" x-model="building.totalArea" 
                                        class="form-input w-full" placeholder="Ex: 5000">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Status:</label>
                                    <select x-model="building.status" class="form-input w-full">
                                        <option value="">Selecione o status</option>
                                        <option value="Ativo">Ativo</option>
                                        <option value="Em Manutenção">Em Manutenção</option>
                                        <option value="Inativo">Inativo</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">Última Inspeção:</label>
                                    <input type="date" x-model="building.lastInspection" 
                                        class="form-input w-full">
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg border border-gray-200 col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Descrição</h3>
                            <textarea x-model="building.description" 
                                class="form-input w-full resize-none" 
                                rows="4" 
                                placeholder="Descrição detalhada do edifício..."></textarea>
                        </div>
                    </div>

                    <!-- Seção de Fotos -->
                    <div class="bg-white p-4 rounded-lg border border-gray-200 mx-4 mb-4">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Fotos do Edifício</h3>
                            <button @click="openPhotoModal()" 
                                class="btn-primary flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                Adicionar Foto
                            </button>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" x-show="building.photos && building.photos.length > 0">
                            <template x-for="(photo, index) in building.photos" :key="photo.id">
                                <div class="photo-container">
                                    <img :src="photo.url" :alt="photo.description" class="w-full h-48 object-cover">
                                    <div class="photo-overlay">
                                        <div class="flex gap-2">
                                            <button @click="editPhoto(index)" 
                                                class="bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                </svg>
                                            </button>
                                            <button @click="removePhoto(index)" 
                                                class="bg-red-600 text-white p-2 rounded-lg hover:bg-red-700">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <polyline points="3,6 5,6 21,6"/>
                                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="p-2 bg-gray-50">
                                        <p class="text-sm text-gray-700" x-text="photo.description"></p>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!building.photos || building.photos.length === 0" 
                            class="text-center py-8 text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                            <p>Nenhuma foto adicionada ainda</p>
                        </div>
                    </div>

                    <!-- Lista de Pavimentos -->
                    <div class="flex flex-col gap-3 p-4">
                        <h3 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">Pavimentos</h3>
                        <template x-for="floor in building.floors" :key="floor.id">
                            <div class="flex items-center gap-4 bg-white px-4 min-h-[72px] py-2 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50"
                                @click="viewFloorDetails(floor.id)">
                                <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg w-14 h-14"
                                    :style="`background-image: url('${floor.photo || '../ícones/disruption.png'}')`">
                                </div>
                                <div class="flex flex-col justify-center flex-1">
                                    <p class="text-[#111518] text-base font-medium leading-normal" x-text="floor.name">
                                    </p>
                                    <p class="text-[#637588] text-sm font-normal leading-normal"
                                        x-text="floor.description"></p>
                                </div>
                                <div class="flex items-center gap-2">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(floor.status)" x-text="floor.status"></span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m9 18 6-6-6-6" />
                                    </svg>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal de Foto -->
        <div x-show="showPhotoModal" x-cloak
            class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900" x-text="photoModalTitle"></h3>
                    <button @click="closePhotoModal()"
                        class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>
                <form @submit.prevent="savePhoto()">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Foto</label>
                            <input type="file" @change="handleFileSelect($event)"
                                accept="image/*" class="form-input w-full">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                            <textarea x-model="currentPhoto.description"
                                class="form-input w-full resize-none"
                                rows="3"
                                placeholder="Descrição da foto..."></textarea>
                        </div>
                    </div>
                    <div class="flex justify-end gap-3 mt-6">
                        <button type="button" @click="closePhotoModal()"
                            class="btn-secondary">
                            Cancelar
                        </button>
                        <button type="submit" class="btn-primary">
                            <span x-text="editingPhotoIndex !== null ? 'Atualizar' : 'Adicionar'"></span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function buildingDetails() {
            return {
                building: {},
                showPhotoModal: false,
                photoModalTitle: 'Adicionar Foto',
                currentPhoto: { description: '', url: '' },
                editingPhotoIndex: null,
                inspectionId: null,

                init() {
                    this.loadBuilding();
                },

                loadBuilding() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const buildingId = urlParams.get('id') || 'EDF-001';
                    this.inspectionId = urlParams.get('inspectionId');

                    // Dados de exemplo do edifício
                    this.building = {
                        id: buildingId,
                        name: 'Edifício Sede',
                        address: 'Rua Principal, 123 - Centro',
                        type: 'Comercial',
                        constructionYear: 2015,
                        totalArea: 5000,
                        status: 'Ativo',
                        lastInspection: '2024-03-15',
                        description: 'Edifício principal da empresa, construído em estrutura de concreto armado com fachada em vidro temperado. Possui sistema de ar condicionado central e gerador de emergência.',
                        photos: [
                            {
                                id: 1,
                                url: '../ícones/disruption.png',
                                description: 'Fachada principal'
                            },
                            {
                                id: 2,
                                url: '../ícones/disruption.png',
                                description: 'Vista lateral'
                            }
                        ],
                        floors: [
                            {
                                id: 'PAV-001',
                                name: 'Térreo',
                                description: 'Pavimento térreo com recepção e áreas comuns',
                                status: 'Ativo',
                                photo: '../ícones/disruption.png'
                            },
                            {
                                id: 'PAV-002',
                                name: '1º Andar',
                                description: 'Primeiro andar com salas comerciais',
                                status: 'Ativo',
                                photo: '../ícones/disruption.png'
                            }
                        ]
                    };
                },

                getStatusColor(status) {
                    switch (status) {
                        case 'Ativo':
                            return 'bg-green-100 text-green-800';
                        case 'Em Manutenção':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Inativo':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                saveBuilding() {
                    // Implementar lógica de salvamento
                    console.log('Salvando edifício:', this.building);
                    alert('Edifício salvo com sucesso!');
                },

                openPhotoModal() {
                    this.showPhotoModal = true;
                    this.photoModalTitle = 'Adicionar Foto';
                    this.currentPhoto = { description: '', url: '' };
                    this.editingPhotoIndex = null;
                },

                editPhoto(index) {
                    this.showPhotoModal = true;
                    this.photoModalTitle = 'Editar Foto';
                    this.currentPhoto = { ...this.building.photos[index] };
                    this.editingPhotoIndex = index;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.currentPhoto = { description: '', url: '' };
                    this.editingPhotoIndex = null;
                },

                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        // Simular upload da foto
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.currentPhoto.url = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                },

                savePhoto() {
                    if (!this.currentPhoto.url || !this.currentPhoto.description) {
                        alert('Por favor, selecione uma foto e adicione uma descrição.');
                        return;
                    }

                    if (this.editingPhotoIndex !== null) {
                        // Editar foto existente
                        this.building.photos[this.editingPhotoIndex] = { ...this.currentPhoto };
                    } else {
                        // Adicionar nova foto
                        const newPhoto = {
                            id: Date.now(),
                            ...this.currentPhoto
                        };
                        this.building.photos.push(newPhoto);
                    }

                    this.closePhotoModal();
                },

                removePhoto(index) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.building.photos.splice(index, 1);
                    }
                },

                viewFloorDetails(floorId) {
                    if (this.inspectionId) {
                        window.location.href = `detalhesPavimento.html?id=${floorId}&inspectionId=${this.inspectionId}&edificio=${this.building.id}`;
                    } else {
                        window.location.href = `detalhesPavimento.html?id=${floorId}&edificio=${this.building.id}`;
                    }
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const inspectionId = urlParams.get('inspectionId');

            if (inspectionId) {
                window.location.href = `detalhesInspecao.html?id=${inspectionId}`;
            } else {
                window.location.href = 'listaInspecoes.html';
            }
        }
    </script>
</body>

</html>
