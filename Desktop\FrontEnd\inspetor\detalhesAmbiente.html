<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes do Ambiente - Inspetor</title>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>    <style>
        [x-cloak] {
            display: none !important;
        }
        .severity-critical { background-color: #fee2e2; color: #991b1b; }
        .severity-high { background-color: #fed7aa; color: #9a3412; }
        .severity-medium { background-color: #fef3c7; color: #92400e; }
        .severity-low { background-color: #dcfce7; color: #166534; }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex items-center gap-2">
                    <button
                        class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900">
                        <img src="../ícones/perfil.png" alt="Perfil" width="32px" height="32px">
                        <span>João Silva</span>
                    </button>
                </div>
            </header>

            <div class="px-40 py-5 flex-1" x-data="environmentDetails()">
                <div class="flex flex-col gap-6">
                    <!-- Header com botão voltar e breadcrumb -->
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" 
                            class="flex items-center gap-2 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="m12 19-7-7 7-7" />
                                <path d="M19 12H5" />
                            </svg>
                            <span class="ml-2">Voltar</span>
                        </button>
                        <div class="flex items-center gap-2 text-sm text-gray-500">
                            <span x-text="building.name"></span>
                            <span>→</span>
                            <span x-text="floor.name"></span>
                            <span>→</span>
                            <span class="text-gray-900 font-medium" x-text="environment.name"></span>
                        </div>
                    </div>

                    <h1 class="text-[#111518] text-2xl font-bold leading-tight" x-text="'Ambiente: ' + environment.name"></h1>

                    <!-- Informações Básicas do Ambiente -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Nome:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Tipo:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Área:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.area + ' m²'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Uso Principal:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.mainUse"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Status da Inspeção</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Status:</span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="{
                                            'bg-green-100 text-green-800': environment.inspectionStatus === 'Completa',
                                            'bg-yellow-100 text-yellow-800': environment.inspectionStatus === 'Em Andamento',
                                            'bg-gray-100 text-gray-800': environment.inspectionStatus === 'Pendente'
                                        }"
                                        x-text="environment.inspectionStatus"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Patologias Encontradas:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.pathologies.length"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Criticidade Máxima:</span>
                                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="{
                                            'severity-critical': environment.maxSeverity === 'Crítica',
                                            'severity-high': environment.maxSeverity === 'Alta',
                                            'severity-medium': environment.maxSeverity === 'Média',
                                            'severity-low': environment.maxSeverity === 'Baixa'
                                        }"
                                        x-text="environment.maxSeverity"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Última Atualização:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="environment.lastUpdate"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Descrição -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrição</h3>
                        <p class="text-sm text-[#637588]" x-text="environment.description"></p>
                    </div>                    <!-- Fotos do Ambiente -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Fotos do Ambiente</h3>
                            <button @click="showUploadModal = true"
                                class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 5v14" />
                                    <path d="M5 12h14" />
                                </svg>
                                Adicionar Foto
                            </button>
                        </div>

                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" x-show="environment.photos && environment.photos.length > 0">
                            <template x-for="(photo, index) in environment.photos" :key="photo.id">
                                <div class="relative group">
                                    <img :src="photo.url" :alt="photo.description" class="w-full h-32 object-cover rounded-lg">
                                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                        <button @click="editPhotoDescription(index)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                                            </svg>
                                        </button>
                                        <button @click="removePhoto(index)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 6h18" />
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                                <path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2" />
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-2 px-1" x-text="photo.description"></p>
                                </div>
                            </template>
                        </div>
                        <div x-show="!environment.photos || environment.photos.length === 0" class="text-center py-8 text-gray-500 col-span-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                            <p>Nenhuma foto adicionada ainda</p>
                        </div>
                    </div>

                    <!-- Lista de Patologias -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Patologias Identificadas</h3>
                            <button @click="addPathology()"
                                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all flex items-center gap-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Adicionar Patologia
                            </button>
                        </div>
                        
                        <div x-show="environment.pathologies.length === 0" class="text-center py-8 text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p>Nenhuma patologia identificada neste ambiente</p>
                        </div>

                        <div class="space-y-4" x-show="environment.pathologies.length > 0">
                            <template x-for="pathology in environment.pathologies" :key="pathology.id">
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-all cursor-pointer"
                                    @click="viewPathologyDetails(pathology.id)">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-3 mb-2">
                                                <h4 class="font-medium text-gray-900" x-text="pathology.name"></h4>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                                    :class="{
                                                        'severity-critical': pathology.severity === 'Crítica',
                                                        'severity-high': pathology.severity === 'Alta',
                                                        'severity-medium': pathology.severity === 'Média',
                                                        'severity-low': pathology.severity === 'Baixa'
                                                    }"
                                                    x-text="pathology.severity"></span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2" x-text="pathology.description"></p>
                                            <div class="flex items-center gap-4 text-xs text-gray-500">
                                                <span>Sistema: <span x-text="pathology.system"></span></span>
                                                <span>Data: <span x-text="pathology.date"></span></span>
                                                <span x-show="pathology.photos && pathology.photos.length > 0">
                                                    📷 <span x-text="pathology.photos.length"></span> foto(s)
                                                </span>
                                            </div>
                                        </div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round" class="text-gray-400">
                                            <path d="m9 18 6-6-6-6" />
                                        </svg>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Sistemas do Ambiente -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Sistemas Presentes</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <template x-for="system in environment.systems" :key="system.id">
                                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900" x-text="system.name"></p>
                                        <p class="text-sm text-gray-600" x-text="system.description"></p>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Modal de Upload de Foto -->
                <div x-show="showUploadModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96">
                        <h3 class="text-lg font-semibold mb-4">Adicionar Foto</h3>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Selecionar arquivo</label>
                            <input type="file" accept="image/*" @change="handleFileSelect($event)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                            <input type="text" x-model="newPhoto.description" placeholder="Digite uma descrição para a foto"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex justify-end gap-3">
                            <button @click="showUploadModal = false; newPhoto = {file: null, description: ''}"
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                                Cancelar
                            </button>                            <button @click="uploadPhoto()"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Adicionar
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Modal de Edição de Descrição da Foto -->
                <div x-show="showPhotoEditModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96">
                        <h3 class="text-lg font-semibold mb-4">Editar Descrição da Foto</h3>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                            <input type="text" x-model="editingPhoto.description"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex justify-end gap-3">
                            <button @click="showPhotoEditModal = false"
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button @click="savePhotoDescription()"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Salvar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function environmentDetails() {
            return {
                building: {},
                floor: {},
                environment: {},
                showUploadModal: false,
                showPhotoEditModal: false,
                newPhoto: {
                    file: null,
                    description: ''
                },
                editingPhoto: {
                    index: -1,
                    description: ''
                },

                init() {
                    this.loadEnvironment();
                },

                loadEnvironment() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const environmentId = urlParams.get('id');
                    const floorId = urlParams.get('floorId');
                    const buildingId = urlParams.get('buildingId');

                    // Dados de exemplo do edifício
                    this.building = {
                        id: buildingId || 'ED-001',
                        name: 'Edifício Sede'
                    };

                    // Dados de exemplo do pavimento
                    this.floor = {
                        id: floorId || 'FL-001',
                        name: 'Térreo'
                    };

                    // Dados de exemplo do ambiente
                    this.environment = {
                        id: environmentId || 'ENV-001',
                        name: 'Recepção Principal',
                        type: 'Área Comum',
                        area: 45,
                        mainUse: 'Atendimento ao público',
                        inspectionStatus: 'Completa',
                        maxSeverity: 'Média',
                        lastUpdate: '15/03/2024',
                        description: 'Área de recepção principal do edifício, com balcão de atendimento, área de espera e sistema de climatização central. Ambiente de grande circulação de pessoas.',
                        photos: [
                            {
                                id: 1,
                                url: 'https://via.placeholder.com/300x300?text=Recepção+Geral',
                                description: 'Vista geral da recepção'
                            },
                            {
                                id: 2,
                                url: 'https://via.placeholder.com/300x300?text=Balcão+Atendimento',
                                description: 'Balcão de atendimento'
                            },
                            {
                                id: 3,
                                url: 'https://via.placeholder.com/300x300?text=Área+Espera',
                                description: 'Área de espera'
                            }
                        ],
                        pathologies: [
                            {
                                id: 1,
                                name: 'Infiltração na parede norte',
                                description: 'Mancha de umidade visível na parede próxima à entrada principal',
                                severity: 'Média',
                                system: 'Estrutural',
                                date: '10/03/2024',
                                photos: ['photo1.jpg', 'photo2.jpg']
                            },
                            {
                                id: 2,
                                name: 'Desgaste no piso',
                                description: 'Riscos e desgaste no revestimento cerâmico devido ao alto tráfego',
                                severity: 'Baixa',
                                system: 'Revestimentos',
                                date: '12/03/2024',
                                photos: ['photo3.jpg']
                            },
                            {
                                id: 3,
                                name: 'Problema na iluminação',
                                description: 'Lâmpadas LED queimadas no setor oeste da recepção',
                                severity: 'Baixa',
                                system: 'Elétrico',
                                date: '14/03/2024',
                                photos: []
                            }
                        ],
                        systems: [
                            {
                                id: 1,
                                name: 'Sistema Elétrico',
                                description: 'Iluminação LED e tomadas'
                            },
                            {
                                id: 2,
                                name: 'Sistema de Climatização',
                                description: 'Ar condicionado central'
                            },
                            {
                                id: 3,
                                name: 'Sistema de Segurança',
                                description: 'Câmeras e controle de acesso'
                            },
                            {
                                id: 4,
                                name: 'Revestimentos',
                                description: 'Piso cerâmico e pintura'
                            }
                        ]
                    };
                },

                viewPathologyDetails(pathologyId) {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesPatologia.html?id=${pathologyId}&environmentId=${this.environment.id}&floorId=${this.floor.id}&buildingId=${this.building.id}&inspectionId=${inspectionId}`;
                },

                addPathology() {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesPatologia.html?new=true&environmentId=${this.environment.id}&floorId=${this.floor.id}&buildingId=${this.building.id}&inspectionId=${inspectionId}`;
                },

                handleFileSelect(event) {
                    this.newPhoto.file = event.target.files[0];
                },

                uploadPhoto() {
                    if (this.newPhoto.file && this.newPhoto.description) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            this.environment.photos.push({
                                id: Date.now(),
                                url: e.target.result,
                                description: this.newPhoto.description
                            });
                        };
                        reader.readAsDataURL(this.newPhoto.file);
                        this.showUploadModal = false;
                        this.newPhoto = { file: null, description: '' };
                    }
                },

                editPhotoDescription(index) {
                    this.editingPhoto = {
                        index: index,
                        description: this.environment.photos[index].description
                    };
                    this.showPhotoEditModal = true;
                },

                savePhotoDescription() {
                    this.environment.photos[this.editingPhoto.index].description = this.editingPhoto.description;
                    this.showPhotoEditModal = false;
                },

                removePhoto(index) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.environment.photos.splice(index, 1);
                    }
                }
            }
        }
    </script>
</body>

</html>