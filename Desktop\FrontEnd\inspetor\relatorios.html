<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - InfraWatch</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link rel="icon" type="image/x-icon" href="../ícones/quote-request.png" />
    <style>
        [x-cloak] {
            display: none !important;
        }

        .nav-item-active {
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
            background-color: #eff6ff;
        }
    </style>
</head>

<body x-data="{ 
    user: { role: 'coordenador' },
    showModal: false,
    formData: {
        inspecaoId: '',
        tipoRelatorio: 'Final',
        titulo: '',
        responsavel: ''
    },
    inspecoes: [
        { id: 'INSP-001', titulo: 'Inspeção Edifício Central', status: 'Concluída' },
        { id: 'INSP-002', titulo: 'Inspeção Prédio Comercial', status: 'Concluída' },
        { id: 'INSP-003', titulo: 'Inspeção Edifício Exemplo', status: 'Concluída' }
    ],
    gerarRelatorio() {
        // Validar formulário
        if (!this.formData.inspecaoId || !this.formData.titulo || !this.formData.responsavel) {
            alert('Por favor, preencha todos os campos obrigatórios.');
            return;
        }
        
        // Em uma aplicação real, aqui seria feita uma requisição para o backend
        // para criar o relatório. Como estamos em um ambiente estático, vamos
        // simular redirecionando para a página de visualização do relatório.
        
        // Gerar um ID único para o relatório (simulado)
        const relId = 'REL-' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        // Redirecionar para a página de visualização do relatório
        window.location.href = `visualizarRelatorio.html?id=${this.formData.inspecaoId}&relId=${relId}&titulo=${encodeURIComponent(this.formData.titulo)}`;
    }
}">
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center nav-item-active">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex justify-end gap-8 items-center">
                    <div class="flex items-center gap-4"
                        x-data="{ 
                            notificationsOpen: false, 
                            hasNotifications: true, 
                            toggleNotifications() { this.notificationsOpen = !this.notificationsOpen; }, 
                            closeNotifications() { this.notificationsOpen = false; } 
                        }">
                        <button @click="toggleNotifications()" class="relative">
                            <div class="text-[#111518]" data-icon="Bell" data-size="24px" data-weight="regular">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor"
                                    viewBox="0 0 256 256">
                                    <path
                                        d="M221.8,175.94C216.25,166.38,208,139.33,208,104a80,80,0,1,0-160,0c0,35.34-8.26,62.38-13.81,71.94A16,16,0,0,0,48,200H88.81a40,40,0,0,0,78.38,0H208a16,16,0,0,0,13.8-24.06ZM128,216a24,24,0,0,1-22.62-16h45.24A24,24,0,0,1,128,216ZM48,184c7.7-13.24,16-34.92,16-80a64,64,0,1,1,128,0c0,45.08,8.3,66.76,16,80Z">
                                    </path>
                                </svg>
                            </div>
                            <div x-show="hasNotifications"
                                class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></div>
                        </button>
                        <div x-show="notificationsOpen" @click.away="closeNotifications()"
                            class="absolute top-16 right-16 mt-2 w-80 rounded-lg bg-white shadow-xl z-20" x-cloak>
                            <div class="px-4 py-2 text-sm font-medium text-gray-700">Notificações</div>
                            <div class="border-t border-gray-200"></div>
                            <div class="max-h-64 overflow-y-auto">
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Nova inspeção #12350 atribuída a você.
                                </a>
                                <a href="#" class="block px-4 py-3 text-sm text-gray-600 hover:bg-gray-100">
                                    Lembrete: Inspeção #12345 vence amanhã.
                                </a>
                                <div x-show="!hasNotifications" class="px-4 py-3 text-sm text-gray-500">
                                    Nenhuma nova notificação.
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="perfil.html">
                        <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
                            style="background-image: url('../ícones/perfil.png');">
                        </div>
                    </a>
                </div>
            </header>

            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
                    <div class="flex flex-wrap justify-between gap-3 p-4 items-center">
                        <p class="text-[#111518] tracking-light text-[32px] font-bold leading-tight min-w-72">
                            Meus Relatórios</p>
                        <button
                            class="flex items-center justify-center gap-2 rounded-xl bg-[#2563eb] px-4 py-2 text-white text-sm font-medium leading-normal tr-duration-300 hover:bg-[#1d4ed8] focus:bg-[#1e40af] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#1e40af] disabled:opacity-50"
                            x-show="user.role === 'coordenador'" @click="showModal = true">
                            <img src="../ícones/mais.png" alt="Gerar Relatório" width="20px" height="20px"
                                style="filter: brightness(0) invert(1);">
                            Gerar Novo Relatório
                        </button>
                    </div>

                    <div class="px-4 py-3">
                        <label class="flex flex-col min-w-40 h-12 w-full">
                            <div class="flex w-full flex-1 items-stretch rounded-xl h-full">
                                <div class="text-[#637688] flex border-none bg-[#f0f2f4] items-center justify-center pl-4 rounded-l-xl border-r-0"
                                    data-icon="MagnifyingGlass" data-size="24px" data-weight="regular">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px"
                                        fill="currentColor" viewBox="0 0 256 256">
                                        <path
                                            d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                                        </path>
                                    </svg>
                                </div>
                                <input placeholder="Buscar relatórios..."
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-r-xl text-[#111518] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] h-full placeholder:text-[#637688] px-4 text-base font-normal leading-normal"
                                    value="" />
                            </div>
                        </label>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 px-4 py-3">
                        <div>
                            <label for="tipoRelatorioFilter" class="block text-sm font-medium text-gray-700">Filtrar por
                                Tipo</label>
                            <select id="tipoRelatorioFilter" name="tipoRelatorioFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                                <option value="">Todos</option>
                                <option value="Preliminar">Preliminar</option>
                                <option value="Final">Final</option>
                                <option value="Complementar">Complementar</option>
                            </select>
                        </div>
                        <div>
                            <label for="startDateFilter" class="block text-sm font-medium text-gray-700">Data de Criação
                                (Início)</label>
                            <input type="date" id="startDateFilter" name="startDateFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                        </div>
                        <div>
                            <label for="endDateFilter" class="block text-sm font-medium text-gray-700">Data de Criação
                                (Fim)</label>
                            <input type="date" id="endDateFilter" name="endDateFilter"
                                class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-[#f0f2f4]">
                        </div>
                    </div>

                    <div class="px-4 py-3 @container">
                        <div class="flex overflow-hidden rounded-xl border border-[#dce1e5] bg-white">
                            <table class="flex-1">
                                <thead>
                                    <tr class="bg-white">
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            ID do Relatório</th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[200px] text-sm font-medium leading-normal">
                                            Inspeção Associada</th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Tipo</th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Data de Criação</th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Gerado Por</th>
                                        <th
                                            class="px-4 py-3 text-center text-[#111518] w-[150px] text-sm font-medium leading-normal">
                                            Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            REL-001</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[200px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-003</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#e6f7ff] text-[#007bff]">Final</span>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            18/05/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Coordenador Silva</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="visualizarRelatorio.html?id=INSP-003&relId=REL-001"
                                                class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>
                                            <a href="#" class="text-green-600 hover:underline">Baixar</a>
                                        </td>
                                    </tr>
                                    <tr class="border-t border-t-[#dce1e5]">
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            REL-002</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[200px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            INSP-001</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <span
                                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#fffbe6] text-[#faad14]">Preliminar</span>
                                        </td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            29/05/2025</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-[#637688] text-sm font-normal leading-normal text-center">
                                            Coordenador Souza</td>
                                        <td
                                            class="h-[72px] px-4 py-2 w-[150px] text-sm font-normal leading-normal text-center">
                                            <a href="visualizarRelatorio.html?id=INSP-001&relId=REL-002"
                                                class="text-blue-600 hover:underline mr-2">Ver Detalhes</a>
                                            <a href="#" class="text-green-600 hover:underline">Baixar</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="flex justify-center px-4 py-5">
                        <nav aria-label="Pagination">
                            <ul class="inline-flex items-center -space-x-px rounded-md text-sm">
                                <li>
                                    <a href="#"
                                        class="inline-flex items-center rounded-l-md border border-gray-300 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 focus:z-20">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                            fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </li>
                                <li>
                                    <a href="#" aria-current="page"
                                        class="z-10 inline-flex items-center border border-blue-500 bg-blue-50 px-3 py-2 text-blue-600 focus:z-20">1</a>
                                </li>
                                <li>
                                    <a href="#"
                                        class="inline-flex items-center border border-gray-300 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 focus:z-20">2</a>
                                </li>
                                <li>
                                    <a href="#"
                                        class="inline-flex items-center rounded-r-md border border-gray-300 bg-white px-3 py-2 text-gray-500 hover:bg-gray-50 focus:z-20">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                                            fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd"
                                                d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50" x-show="showModal"
        x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" x-cloak>
        <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded-lg shadow-lg overflow-y-auto"
            x-show="showModal" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-200"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90" @click.away="showModal = false">
            <div class="modal-content py-4 text-left px-6">
                <div class="flex justify-between items-center pb-3 border-b">
                    <p class="text-xl font-bold text-gray-700">Gerar Novo Relatório</p>
                    <button class="modal-close cursor-pointer z-50 text-gray-500 hover:text-gray-700"
                        @click="showModal = false">
                        <svg class="fill-current" xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                            viewBox="0 0 18 18">
                            <path
                                d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z">
                            </path>
                        </svg>
                    </button>
                </div>

                <div class="my-4">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="inspecaoId">
                            Inspeção Associada*
                        </label>
                        <select id="inspecaoId"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            x-model="formData.inspecaoId">
                            <option value="">Selecione uma inspeção</option>
                            <template x-for="inspecao in inspecoes" :key="inspecao.id">
                                <option :value="inspecao.id" x-text="`${inspecao.id} - ${inspecao.titulo}`"></option>
                            </template>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="tipoRelatorio">
                            Tipo de Relatório*
                        </label>
                        <select id="tipoRelatorio"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            x-model="formData.tipoRelatorio">
                            <option value="Preliminar">Preliminar</option>
                            <option value="Final">Final</option>
                            <option value="Complementar">Complementar</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="titulo">
                            Título do Relatório*
                        </label>
                        <input id="titulo" type="text"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            placeholder="Ex: Avaliação Estrutural Edifício Exemplo" x-model="formData.titulo">
                    </div>
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="responsavel">
                            Responsável Técnico*
                        </label>
                        <input id="responsavel" type="text"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            placeholder="Nome do responsável técnico" x-model="formData.responsavel">
                    </div>
                </div>

                <div class="flex justify-end pt-2 border-t">
                    <button
                        class="px-4 bg-gray-200 p-3 rounded-lg text-gray-600 mr-2 hover:bg-gray-300 focus:outline-none"
                        @click="showModal = false">
                        Cancelar
                    </button>
                    <button class="px-4 bg-blue-600 p-3 rounded-lg text-white hover:bg-blue-700 focus:outline-none"
                        @click="gerarRelatorio()">
                        Gerar Relatório
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>