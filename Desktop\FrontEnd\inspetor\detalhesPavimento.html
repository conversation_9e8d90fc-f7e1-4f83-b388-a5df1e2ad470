<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes do Pavimento - Inspetor</title>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>    <style>
        [x-cloak] {
            display: none !important;
        }
        .severity-critical { background-color: #fee2e2; color: #991b1b; }
        .severity-high { background-color: #fed7aa; color: #9a3412; }
        .severity-medium { background-color: #fef3c7; color: #92400e; }
        .severity-low { background-color: #dcfce7; color: #166534; }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex items-center gap-2">
                    <button
                        class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900">
                        <img src="../ícones/perfil.png" alt="Perfil" width="32px" height="32px">
                        <span>João Silva</span>
                    </button>
                </div>
            </header>

            <div class="px-40 py-5 flex-1" x-data="floorDetails()">
                <div class="flex flex-col gap-6">
                    <!-- Header com botão voltar e breadcrumb -->
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" 
                            class="flex items-center gap-2 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="m12 19-7-7 7-7" />
                                <path d="M19 12H5" />
                            </svg>
                            <span class="ml-2">Voltar</span>
                        </button>
                        <div class="flex items-center gap-2 text-sm text-gray-500">
                            <span x-text="floor.buildingName"></span>
                            <span>→</span>
                            <span class="text-gray-900 font-medium" x-text="floor.name"></span>
                        </div>
                    </div>

                    <h1 class="text-[#111518] text-2xl font-bold leading-tight" x-text="'Pavimento: ' + floor.name"></h1>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Nome:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.name"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Edifício:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.buildingName"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Tipo:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Nível:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.level"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Características</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Área Total:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.totalArea + ' m²'"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Número de Ambientes:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.environments.length"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Status:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(floor.status)" x-text="floor.status"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Última Inspeção:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="floor.lastInspection || 'Nunca'"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5] col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrição</h3>
                            <textarea x-model="floor.description" 
                                class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                rows="3"
                                placeholder="Adicione uma descrição do pavimento..."></textarea>
                            <button @click="saveDescription()" 
                                class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                Salvar Descrição
                            </button>
                        </div>
                    </div>

                    <!-- Fotos do Pavimento -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Fotos do Pavimento</h3>
                            <button @click="openPhotoModal()" 
                                class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 5v14" />
                                    <path d="M5 12h14" />
                                </svg>
                                Adicionar Foto
                            </button>
                        </div>                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" x-show="floor.photos && floor.photos.length > 0">
                            <template x-for="photo in floor.photos" :key="photo.id">
                                <div class="relative group">
                                    <img :src="photo.url" :alt="photo.description"
                                        class="w-full h-32 object-cover rounded-lg">
                                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                        <button @click="editPhoto(photo)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                                            </svg>
                                        </button>
                                        <button @click="removePhoto(photo.id)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 6h18" />
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                                <path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!floor.photos || floor.photos.length === 0" class="text-center py-8 text-gray-500 col-span-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                            <p>Nenhuma foto adicionada ainda</p>
                        </div>
                    </div>

                    <!-- Lista de Ambientes -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Ambientes</h3>
                        <div class="space-y-3">
                            <template x-for="environment in floor.environments" :key="environment.id">
                                <div class="flex items-center gap-4 p-4 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50"
                                    @click="viewEnvironmentDetails(environment.id)">
                                    <div class="bg-center bg-no-repeat aspect-square bg-cover rounded-lg w-12 h-12"
                                        :style="`background-image: url('${environment.photo || '../ícones/disruption.png'}')`">
                                    </div>
                                    <div class="flex flex-col justify-center flex-1">
                                        <p class="text-[#111518] text-base font-medium leading-normal" x-text="environment.name">
                                        </p>
                                        <p class="text-[#637588] text-sm font-normal leading-normal"
                                            x-text="environment.description"></p>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                            :class="getStatusColor(environment.status)" x-text="environment.status"></span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                            stroke-linejoin="round">
                                            <path d="m9 18 6-6-6-6" />
                                        </svg>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Modal para adicionar/editar fotos -->
                <div x-show="showPhotoModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96 max-w-full">
                        <h3 class="text-lg font-semibold mb-4" x-text="editingPhoto ? 'Editar Foto' : 'Adicionar Nova Foto'"></h3>
                        
                        <div class="space-y-4">
                            <div x-show="!editingPhoto">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Selecionar Arquivo</label>
                                <input type="file" @change="handleFileSelect($event)" accept="image/*" 
                                    class="w-full p-2 border border-gray-300 rounded-lg">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                                <textarea x-model="newPhotoDescription" 
                                    class="w-full p-2 border border-gray-300 rounded-lg resize-none"
                                    rows="3" placeholder="Descreva esta foto..."></textarea>
                            </div>
                        </div>

                        <div class="flex justify-end gap-2 mt-6">
                            <button @click="closePhotoModal()" 
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button @click="savePhoto()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <span x-text="editingPhoto ? 'Atualizar' : 'Salvar'"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function floorDetails() {
            return {
                floor: {},
                showPhotoModal: false,
                editingPhoto: null,
                newPhotoDescription: '',
                selectedFile: null,

                init() {
                    this.loadFloor();
                },

                loadFloor() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const floorId = urlParams.get('id');

                    // Dados de exemplo do pavimento
                    this.floor = {
                        id: floorId || 'PAV-001',
                        name: 'Térreo',
                        buildingName: 'Edifício Sede',
                        type: 'Comercial',
                        level: '0',
                        totalArea: 500,
                        status: 'Ativo',
                        lastInspection: '15/03/2024',
                        description: 'Pavimento térreo com área de recepção, salas comerciais e estacionamento. Possui pé-direito de 3,5m e estrutura de concreto armado.',
                        photos: [
                            {
                                id: 1,
                                url: '../ícones/disruption.png',
                                description: 'Vista geral do pavimento'
                            },
                            {
                                id: 2,
                                url: '../ícones/disruption.png',
                                description: 'Área de recepção'
                            }
                        ],
                        environments: [
                            {
                                id: 'AMB-001',
                                name: 'Recepção',
                                description: 'Área de recepção e atendimento ao público',
                                status: 'Ativo',
                                photo: '../ícones/disruption.png'
                            },
                            {
                                id: 'AMB-002',
                                name: 'Sala 01',
                                description: 'Sala comercial com 50m²',
                                status: 'Ativo',
                                photo: '../ícones/disruption.png'
                            },
                            {
                                id: 'AMB-003',
                                name: 'Estacionamento',
                                description: 'Área de estacionamento coberto',
                                status: 'Em Manutenção',
                                photo: '../ícones/disruption.png'
                            }
                        ]
                    };
                },

                getStatusColor(status) {
                    switch (status) {
                        case 'Ativo':
                            return 'bg-green-100 text-green-800';
                        case 'Em Manutenção':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Inativo':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                saveDescription() {
                    // Simular salvamento da descrição
                    alert('Descrição salva com sucesso!');
                },

                openPhotoModal() {
                    this.showPhotoModal = true;
                    this.editingPhoto = null;
                    this.newPhotoDescription = '';
                    this.selectedFile = null;
                },

                editPhoto(photo) {
                    this.editingPhoto = photo;
                    this.newPhotoDescription = photo.description;
                    this.showPhotoModal = true;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.editingPhoto = null;
                    this.newPhotoDescription = '';
                    this.selectedFile = null;
                },

                handleFileSelect(event) {
                    this.selectedFile = event.target.files[0];
                },

                savePhoto() {
                    if (this.editingPhoto) {
                        // Atualizar foto existente
                        this.editingPhoto.description = this.newPhotoDescription;
                        alert('Foto atualizada com sucesso!');
                    } else {
                        // Adicionar nova foto
                        if (!this.selectedFile) {
                            alert('Por favor, selecione um arquivo.');
                            return;
                        }
                        
                        const newPhoto = {
                            id: Date.now(),
                            url: '../ícones/disruption.png', // Em produção, seria a URL do arquivo carregado
                            description: this.newPhotoDescription || 'Sem descrição'
                        };
                        
                        if (!this.floor.photos) {
                            this.floor.photos = [];
                        }
                        this.floor.photos.push(newPhoto);
                        alert('Foto adicionada com sucesso!');
                    }
                    
                    this.closePhotoModal();
                },

                removePhoto(photoId) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.floor.photos = this.floor.photos.filter(photo => photo.id !== photoId);
                        alert('Foto removida com sucesso!');
                    }
                },

                viewEnvironmentDetails(environmentId) {
                    window.location.href = `detalhesAmbiente.html?id=${environmentId}`;
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const buildingId = urlParams.get('buildingId');
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');

            if (buildingId) {
                window.location.href = `detalhesEdificio.html?id=${buildingId}&mode=${mode}&from=${from}&inspectionId=${inspectionId}`;
            } else {
                window.location.href = 'listaInspecoes.html';
            }
        }
    </script>
</body>

</html>
