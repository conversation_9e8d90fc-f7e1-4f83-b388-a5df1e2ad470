<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>InfraWatch - Login</title>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Work+Sans%3Awght%40400%3B500%3B700%3B900" />
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Work Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111418]">
                    <h2 class="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em]">InfraWatch</h2>
                </div>
            </header>
            <div class="px-40 flex flex-1 justify-center py-5">
                <div class="layout-content-container flex flex-col w-[512px] max-w-[512px] py-5 max-w-[960px] flex-1">
                    <h2
                        class="text-[#111418] tracking-light text-[28px] font-bold leading-tight px-4 text-center pb-3 pt-5">
                        Login</h2>
                    <form id="loginForm" class="flex flex-col items-center w-full">
                        <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                            <label class="flex flex-col min-w-40 flex-1">
                                <p class="text-[#111418] text-base font-medium leading-normal pb-2">Email</p>
                                <input placeholder="<EMAIL>" type="email" id="email" name="email"
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                    value="" />
                            </label>
                        </div>
                        <div class="flex w-full max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
                            <label class="flex flex-col min-w-40 flex-1">
                                <p class="text-[#111418] text-base font-medium leading-normal pb-2">Senha</p>
                                <input placeholder="Senha" type="password" id="senha" name="senha"
                                    class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#111418] focus:outline-0 focus:ring-0 border-none bg-[#f0f2f4] focus:border-none h-14 placeholder:text-[#637588] p-4 text-base font-normal leading-normal"
                                    value="" />
                            </label>
                        </div>
                        <div class="flex w-full max-w-[480px] px-4 py-3">
                            <button type="submit"
                                class="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 flex-1 bg-[#1572cf] text-white text-sm font-bold leading-normal tracking-[0.015em]">
                                <span class="truncate">Entrar</span>
                            </button>
                        </div>
                        <div id="errorMessage" class="mt-4 text-center text-red-500 text-sm"></div>
                        <div class="mt-6 text-center px-4 w-full max-w-[480px]">
                            <a href="recuperarSenha.html"
                                class="text-[#637588] text-sm font-normal leading-normal underline">Problemas para
                                entrar?</a>
                        </div>
                        <div class="mt-4 text-center px-4 pb-3 w-full max-w-[480px]">
                            <p class="text-sm text-[#637588] font-normal leading-normal">Não tem uma conta? <a
                                    href="cadastro.html" class="text-[#1572cf] font-medium underline">Crie seu
                                    Cadastro</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', function (event) {
                event.preventDefault();
                const emailInput = document.getElementById('email');
                const passwordInput = document.getElementById('senha');
                const errorMessageElement = document.getElementById('errorMessage');

                const email = emailInput.value.trim();
                const password = passwordInput.value.trim();

                errorMessageElement.textContent = '';

                if (email === '<EMAIL>' && password === '123456') {
                    window.location.href = '../admin/dashboard.html';
                } else if (email === '<EMAIL>' && password === '123456') {
                    window.location.href = '../inspetor/listaInspecoes.html';
                } else {
                    errorMessageElement.textContent = 'Email ou senha inválidos.';
                }
            });
        }
    });
</script>

</html>