<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Public+Sans%3Awght%40400%3B500%3B700%3B900" />
    <title>Detalhes da Patologia - Inspetor</title>
    <link rel="icon" type="image/x-icon" href="../ícones/inspecoes.png" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>    <style>
        [x-cloak] {
            display: none !important;
        }
        .severity-critical { background-color: #fee2e2; color: #991b1b; }
        .severity-high { background-color: #fed7aa; color: #9a3412; }
        .severity-medium { background-color: #fef3c7; color: #92400e; }
        .severity-low { background-color: #dcfce7; color: #166534; }
    </style>
</head>

<body>
    <div class="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden"
        style='font-family: "Public Sans", "Noto Sans", sans-serif;'>
        <div class="layout-container flex h-full grow flex-col">
            <header
                class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f0f2f4] px-10 py-3">
                <div class="flex items-center gap-4 text-[#111518]">
                    <a href="listaInspecoes.html">
                        <h2 class="text-[#111518] text-lg font-bold leading-tight tracking-[-0.015em]">InfraWatch
                        </h2>
                    </a>
                </div>

                <nav class="flex flex-1 justify-center">
                    <div class="flex flex-row gap-x-6 items-center">
                        <a href="listaInspecoes.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/inspecoes.png" alt="Inspeções" width="20px" height="20px">
                            Inspeções
                        </a>
                        <a href="relatorios.html"
                            class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900 w-32 justify-center">
                            <img src="../ícones/quote-request.png" alt="Relatórios" width="20px" height="20px">
                            Relatórios
                        </a>
                    </div>
                </nav>

                <div class="flex items-center gap-2">
                    <button
                        class="flex items-center gap-2 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-200 hover:text-gray-900">
                        <img src="../ícones/perfil.png" alt="Perfil" width="32px" height="32px">
                        <span>João Silva</span>
                    </button>
                </div>
            </header>

            <div class="px-40 py-5 flex-1" x-data="pathologyDetails()">
                <div class="flex flex-col gap-6">                    <!-- Header com botão voltar e breadcrumb -->
                    <div class="flex items-center gap-4">
                        <button onclick="history.back()" 
                            class="flex items-center gap-2 text-gray-600 hover:text-gray-900">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round">
                                <path d="m12 19-7-7 7-7" />
                                <path d="M19 12H5" />
                            </svg>
                            <span class="ml-2">Voltar</span>
                        </button>
                        <div class="flex items-center gap-2 text-sm text-gray-500">
                            <span x-text="pathology.buildingName"></span>
                            <span>→</span>
                            <span x-text="pathology.floorName"></span>
                            <span>→</span>
                            <span x-text="pathology.environmentName"></span>
                            <span>→</span>
                            <span class="text-gray-900 font-medium" x-text="'Patologia ' + pathology.id"></span>
                        </div>
                    </div>

                    <h1 class="text-[#111518] text-2xl font-bold leading-tight" x-text="'Patologia: ' + pathology.type"></h1>

                    <!-- Informações Básicas -->
                    <div class="grid grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Informações Básicas</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">ID:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="pathology.id"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Tipo:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="pathology.type"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Sistema Afetado:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="pathology.affectedSystem"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Data de Identificação:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="pathology.identificationDate"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Classificação</h3>
                            <div class="space-y-3">
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Severidade:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getSeverityColor(pathology.severity)" x-text="pathology.severity"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Urgência:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getUrgencyColor(pathology.urgency)" x-text="pathology.urgency"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Status:</span>
                                    <span
                                        class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        :class="getStatusColor(pathology.status)" x-text="pathology.status"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-[#637588]">Responsável:</span>
                                    <span class="ml-2 text-sm text-[#111518]" x-text="pathology.responsible"></span>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg border border-[#dce0e5] col-span-2">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Descrição</h3>
                            <textarea x-model="pathology.description" 
                                class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                rows="4"
                                placeholder="Descreva a patologia encontrada..."></textarea>
                            <button @click="saveDescription()" 
                                class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                                Salvar Descrição
                            </button>
                        </div>
                    </div>

                    <!-- Análise Técnica -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Análise Técnica</h3>
                        <div class="grid grid-cols-2 gap-6">
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">Possível Causa</h4>
                                <textarea x-model="pathology.possibleCause" 
                                    class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    rows="3"
                                    placeholder="Identifique a possível causa..."></textarea>
                            </div>
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">Recomendações</h4>
                                <textarea x-model="pathology.recommendations" 
                                    class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    rows="3"
                                    placeholder="Suas recomendações para correção..."></textarea>
                            </div>
                        </div>                        <button @click="saveAnalysis()" 
                            class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                            Salvar Análise
                        </button>
                    </div>

                    <!-- Fotos da Patologia -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Fotos da Patologia</h3>
                            <button @click="openPhotoModal()" 
                                class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 5v14" />
                                    <path d="M5 12h14" />
                                </svg>
                                Adicionar Foto
                            </button>
                        </div>                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4" x-show="pathology.photos && pathology.photos.length > 0">
                            <template x-for="photo in pathology.photos" :key="photo.id">
                                <div class="relative group">
                                    <img :src="photo.url" :alt="photo.description"
                                        class="w-full h-32 object-cover rounded-lg">
                                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                                        <button @click="editPhoto(photo)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z" />
                                            </svg>
                                        </button>
                                        <button @click="removePhoto(photo.id)" class="bg-white bg-opacity-20 backdrop-blur-sm rounded-full p-2 text-white hover:bg-opacity-30 transition-all">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 6h18" />
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                                                <path d="M8 6V4c0-1 1-2 2-2h4c-1 0 2 1 2 2v2" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!pathology.photos || pathology.photos.length === 0" class="text-center py-8 text-gray-500 col-span-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-2 text-gray-400">
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                            </svg>
                            <p>Nenhuma foto adicionada ainda</p>
                        </div>
                    </div>

                    <!-- Histórico de Acompanhamento -->
                    <div class="bg-white p-6 rounded-lg border border-[#dce0e5]">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Histórico de Acompanhamento</h3>
                            <button @click="addFollowUp()" 
                                class="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 5v14" />
                                    <path d="M5 12h14" />
                                </svg>
                                Adicionar Acompanhamento
                            </button>
                        </div>
                        <div class="space-y-4" x-show="pathology.followUps && pathology.followUps.length > 0">
                            <template x-for="followUp in pathology.followUps" :key="followUp.id">
                                <div class="border-l-4 border-blue-200 pl-4 py-2">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-[#111518]" x-text="followUp.date + ' - ' + followUp.author"></p>
                                            <p class="text-sm text-[#637588] mt-1" x-text="followUp.description"></p>
                                        </div>
                                        <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded" x-text="followUp.type"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div x-show="!pathology.followUps || pathology.followUps.length === 0" class="text-center py-8 text-[#637588]">
                            Nenhum acompanhamento registrado.
                        </div>
                    </div>
                </div>

                <!-- Modal para adicionar/editar fotos -->
                <div x-show="showPhotoModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96 max-w-full">
                        <h3 class="text-lg font-semibold mb-4" x-text="editingPhoto ? 'Editar Foto' : 'Adicionar Nova Foto'"></h3>
                        
                        <div class="space-y-4">
                            <div x-show="!editingPhoto">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Selecionar Arquivo</label>
                                <input type="file" @change="handleFileSelect($event)" accept="image/*" 
                                    class="w-full p-2 border border-gray-300 rounded-lg">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                                <textarea x-model="newPhotoDescription" 
                                    class="w-full p-2 border border-gray-300 rounded-lg resize-none"
                                    rows="3" placeholder="Descreva esta foto da patologia..."></textarea>
                            </div>
                        </div>

                        <div class="flex justify-end gap-2 mt-6">
                            <button @click="closePhotoModal()" 
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button @click="savePhoto()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <span x-text="editingPhoto ? 'Atualizar' : 'Salvar'"></span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Modal para adicionar acompanhamento -->
                <div x-show="showFollowUpModal" x-cloak class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div class="bg-white rounded-lg p-6 w-96 max-w-full">
                        <h3 class="text-lg font-semibold mb-4">Adicionar Acompanhamento</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
                                <select x-model="newFollowUpType" class="w-full p-2 border border-gray-300 rounded-lg">
                                    <option value="Inspeção">Inspeção</option>
                                    <option value="Manutenção">Manutenção</option>
                                    <option value="Observação">Observação</option>
                                    <option value="Correção">Correção</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Descrição</label>
                                <textarea x-model="newFollowUpDescription" 
                                    class="w-full p-2 border border-gray-300 rounded-lg resize-none"
                                    rows="4" placeholder="Descreva o acompanhamento..."></textarea>
                            </div>
                        </div>

                        <div class="flex justify-end gap-2 mt-6">
                            <button @click="closeFollowUpModal()" 
                                class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                                Cancelar
                            </button>
                            <button @click="saveFollowUp()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                Salvar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function pathologyDetails() {
            return {
                pathology: {},
                showPhotoModal: false,
                showFollowUpModal: false,
                editingPhoto: null,
                newPhotoDescription: '',
                selectedFile: null,
                newFollowUpType: 'Inspeção',
                newFollowUpDescription: '',

                init() {
                    this.loadPathology();
                },

                loadPathology() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const pathologyId = urlParams.get('id');

                    // Dados de exemplo da patologia
                    this.pathology = {
                        id: pathologyId || 'PAT-001',
                        type: 'Infiltração',
                        affectedSystem: 'Sistema Hidráulico',
                        identificationDate: '10/03/2024',
                        severity: 'Alta',
                        urgency: 'Urgente',
                        status: 'Em Análise',
                        responsible: 'João Silva',
                        buildingId: 'ED-001',
                        buildingName: 'Edifício Sede',
                        floorId: 'PAV-001',
                        floorName: 'Térreo',
                        environmentId: 'AMB-001',
                        environmentName: 'Recepção',
                        description: 'Infiltração identificada na parede norte da recepção, causando manchas de umidade e descascamento da tinta. A área afetada tem aproximadamente 2m².',
                        possibleCause: 'Possível vazamento na tubulação hidráulica embutida na parede ou problema na impermeabilização externa.',
                        recommendations: 'Realizar investigação para localizar origem do vazamento. Verificar tubulação com teste de pressão. Avaliar necessidade de reparo na impermeabilização externa.',
                        photos: [
                            {
                                id: 1,
                                url: '../ícones/disruption.png',
                                description: 'Vista geral da infiltração'
                            },
                            {
                                id: 2,
                                url: '../ícones/disruption.png',
                                description: 'Detalhe das manchas de umidade'
                            }
                        ],
                        followUps: [
                            {
                                id: 1,
                                date: '10/03/2024',
                                author: 'João Silva',
                                type: 'Inspeção',
                                description: 'Primeira identificação da patologia durante inspeção de rotina.'
                            },
                            {
                                id: 2,
                                date: '12/03/2024',
                                author: 'João Silva',
                                type: 'Observação',
                                description: 'Mancha de umidade aumentou em aproximadamente 20% desde a última inspeção.'
                            }
                        ]
                    };
                },

                getSeverityColor(severity) {
                    switch (severity) {
                        case 'Baixa':
                            return 'bg-green-100 text-green-800';
                        case 'Média':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Alta':
                            return 'bg-red-100 text-red-800';
                        case 'Crítica':
                            return 'bg-red-200 text-red-900';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                getUrgencyColor(urgency) {
                    switch (urgency) {
                        case 'Baixa':
                            return 'bg-blue-100 text-blue-800';
                        case 'Normal':
                            return 'bg-gray-100 text-gray-800';
                        case 'Urgente':
                            return 'bg-orange-100 text-orange-800';
                        case 'Emergencial':
                            return 'bg-red-100 text-red-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                getStatusColor(status) {
                    switch (status) {
                        case 'Identificada':
                            return 'bg-yellow-100 text-yellow-800';
                        case 'Em Análise':
                            return 'bg-blue-100 text-blue-800';
                        case 'Em Correção':
                            return 'bg-orange-100 text-orange-800';
                        case 'Corrigida':
                            return 'bg-green-100 text-green-800';
                        case 'Monitoramento':
                            return 'bg-purple-100 text-purple-800';
                        default:
                            return 'bg-gray-100 text-gray-800';
                    }
                },

                saveDescription() {
                    alert('Descrição salva com sucesso!');
                },

                saveAnalysis() {
                    alert('Análise técnica salva com sucesso!');
                },

                openPhotoModal() {
                    this.showPhotoModal = true;
                    this.editingPhoto = null;
                    this.newPhotoDescription = '';
                    this.selectedFile = null;
                },

                editPhoto(photo) {
                    this.editingPhoto = photo;
                    this.newPhotoDescription = photo.description;
                    this.showPhotoModal = true;
                },

                closePhotoModal() {
                    this.showPhotoModal = false;
                    this.editingPhoto = null;
                    this.newPhotoDescription = '';
                    this.selectedFile = null;
                },

                handleFileSelect(event) {
                    this.selectedFile = event.target.files[0];
                },

                savePhoto() {
                    if (this.editingPhoto) {
                        this.editingPhoto.description = this.newPhotoDescription;
                        alert('Foto atualizada com sucesso!');
                    } else {
                        if (!this.selectedFile) {
                            alert('Por favor, selecione um arquivo.');
                            return;
                        }
                        
                        const newPhoto = {
                            id: Date.now(),
                            url: '../ícones/disruption.png',
                            description: this.newPhotoDescription || 'Sem descrição'
                        };
                        
                        if (!this.pathology.photos) {
                            this.pathology.photos = [];
                        }
                        this.pathology.photos.push(newPhoto);
                        alert('Foto adicionada com sucesso!');
                    }
                    
                    this.closePhotoModal();
                },

                removePhoto(photoId) {
                    if (confirm('Tem certeza que deseja remover esta foto?')) {
                        this.pathology.photos = this.pathology.photos.filter(photo => photo.id !== photoId);
                        alert('Foto removida com sucesso!');
                    }
                },

                addFollowUp() {
                    this.showFollowUpModal = true;
                    this.newFollowUpType = 'Inspeção';
                    this.newFollowUpDescription = '';
                },

                closeFollowUpModal() {
                    this.showFollowUpModal = false;
                    this.newFollowUpType = 'Inspeção';
                    this.newFollowUpDescription = '';
                },

                saveFollowUp() {
                    if (!this.newFollowUpDescription.trim()) {
                        alert('Por favor, adicione uma descrição.');
                        return;
                    }

                    const newFollowUp = {
                        id: Date.now(),
                        date: new Date().toLocaleDateString('pt-BR'),
                        author: 'João Silva', // Em produção, seria o usuário logado
                        type: this.newFollowUpType,
                        description: this.newFollowUpDescription
                    };

                    if (!this.pathology.followUps) {
                        this.pathology.followUps = [];
                    }
                    this.pathology.followUps.push(newFollowUp);
                    
                    alert('Acompanhamento adicionado com sucesso!');
                    this.closeFollowUpModal();
                },

                viewBuildingDetails(buildingId) {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesEdificio.html?id=${buildingId}&inspectionId=${inspectionId}`;
                },

                viewFloorDetails(floorId) {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesPavimento.html?id=${floorId}&inspectionId=${inspectionId}`;
                },

                viewEnvironmentDetails(environmentId) {
                    const inspectionId = new URLSearchParams(window.location.search).get('inspectionId');
                    window.location.href = `detalhesAmbiente.html?id=${environmentId}&inspectionId=${inspectionId}`;
                }
            }
        }

        function goBack() {
            const urlParams = new URLSearchParams(window.location.search);
            const systemId = urlParams.get('systemId');
            const environmentId = urlParams.get('environmentId');
            const mode = urlParams.get('mode');
            const from = urlParams.get('from');
            const inspectionId = urlParams.get('inspectionId');

            if (systemId) {
                window.location.href = `detalhesSistema.html?id=${systemId}&mode=${mode}&from=${from}&inspectionId=${inspectionId}`;
            } else if (environmentId) {
                window.location.href = `detalhesAmbiente.html?id=${environmentId}&mode=${mode}&from=${from}&inspectionId=${inspectionId}`;
            } else {
                window.location.href = 'listaInspecoes.html';
            }
        }
    </script>
</body>

</html>
